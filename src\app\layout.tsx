import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import Script from "next/script";

export const metadata: Metadata = {
  title: "JavaFiddle - Build and share Java code snippets in your browser",
  description:
    "JavaFiddle is an online, browser-based Java IDE. Create and share Swing applications for free!",
  icons: {
    icon: "/favicon.ico",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="darkreader-lock" />
        <Script
          src="https://cjrtnc.leaningtech.com/4.2/loader.js"
          strategy="beforeInteractive"
        />
      </head>
      <body className="overflow-hidden font-sans">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="contents">{children}</div>
        </ThemeProvider>
      </body>
    </html>
  );
}
