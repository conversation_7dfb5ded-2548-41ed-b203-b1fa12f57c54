{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/theme-provider.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\nimport { type ThemeProviderProps } from 'next-themes/dist/types';\n\nexport function ThemeProvider({ children, ...props }: ThemeProviderProps) {\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKO,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAA2B;IACtE,qBAAO,8OAAC,gJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC", "debugId": null}}]}