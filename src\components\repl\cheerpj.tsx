'use client';

import { useEffect, useRef } from 'react';
import { useReplStore } from '@/lib/stores/repl-store';
import { debounceFunction } from '@/lib/utilities';

export function CheerpJ() {
  const cjConsoleRef = useRef<HTMLElement | null>(null);
  const cjOutputRef = useRef<HTMLElement | null>(null);
  const cjOutputObserverRef = useRef<MutationObserver | null>(null);
  const debounceRunCheerpjRef = useRef<(() => void) | null>(null);
  
  const {
    files,
    autoRun,
    isRunning,
    isSaved,
    runCode,
    setIsRunning,
    setIsSaved,
    setRunCode,
    setCompileLog,
    setFiles,
  } = useReplStore();

  const startCheerpj = async () => {
    await cheerpjInit({
      status: 'none',
    });
    const display = document.getElementById("output");
    cheerpjCreateDisplay(-1, -1, display);
  };

  const deriveMainClass = (file: { path: string; content: string }) => {
    const match = file.content.match(/class\s+(\w+)/);
    return match ? `fiddle.${match[1]}` : 'fiddle.Main';
  };

  const runCheerpj = async () => {
    if (isRunning) return;

    console.info('compileAndRun');
    setIsRunning(true);
    
    if (cjConsoleRef.current) cjConsoleRef.current.innerHTML = '';
    if (cjOutputRef.current) cjOutputRef.current.innerHTML = '';

    const classPath = '/app/tools.jar:/files/';
    const sourceFiles = files.map((file) => '/str/' + file.path);
    
    try {
      const code = await cheerpjRunMain(
        'com.sun.tools.javac.Main',
        classPath,
        ...sourceFiles,
        '-d',
        '/files/',
        '-Xlint'
      );
      
      if (code === 0) {
        await cheerpjRunMain(deriveMainClass(files[0]), classPath);
      }
    } catch (error) {
      console.error('CheerpJ execution error:', error);
    }

    // In case nothing is written on cjConsole and cjOutput
    // manually unflag isRunning
    if (isRunning) {
      setIsRunning(false);
    }
    
    if (cjConsoleRef.current) {
      setCompileLog(cjConsoleRef.current.innerText);
    }
  };

  useEffect(() => {
    const initializeCheerpJ = async () => {
      await startCheerpj();

      cjConsoleRef.current = document.getElementById("console");
      cjOutputRef.current = document.getElementById("cheerpjDisplay");
      
      // Remove useless loading screen
      if (cjOutputRef.current) {
        cjOutputRef.current.classList.remove("cheerpjLoading");
      }

      // Create debounced version of runCheerpj
      debounceRunCheerpjRef.current = debounceFunction(runCheerpj, 1000);

      // Code execution (flagged by isRunning) is considered over
      // when cjConsole or cjOutput are updated
      cjOutputObserverRef.current = new MutationObserver(() => {
        if (isRunning && (
          (cjConsoleRef.current && cjConsoleRef.current.innerHTML) || 
          (cjOutputRef.current && cjOutputRef.current.innerHTML)
        )) {
          setIsRunning(false);
          if (!isSaved) {
            // Trigger files update to refresh state
            setFiles([...files]);
          }
        }
      });

      if (cjConsoleRef.current) {
        cjOutputObserverRef.current.observe(cjConsoleRef.current, {
          childList: true,
          subtree: true,
        });
      }

      if (cjOutputRef.current) {
        cjOutputObserverRef.current.observe(cjOutputRef.current, {
          childList: true,
          subtree: true,
        });
      }

      // Initial run
      await runCheerpj();
    };

    initializeCheerpJ();

    return () => {
      if (cjOutputObserverRef.current) {
        cjOutputObserverRef.current.disconnect();
      }
    };
  }, []);

  // Handle file changes
  useEffect(() => {
    if (isRunning) {
      setIsSaved(false);
    } else {
      try {
        const encoder = new TextEncoder();
        for (const file of files) {
          cheerpOSAddStringFile('/str/' + file.path, encoder.encode(file.content));
        }
        setIsSaved(true);
        if (autoRun) {
          setRunCode(true);
        }
      } catch (error) {
        console.error('Error writing files to CheerpJ', error);
      }
    }
  }, [files, isRunning, autoRun, setIsSaved, setRunCode]);

  // Handle run code trigger
  useEffect(() => {
    if (runCode) {
      setRunCode(false);
      if (autoRun && debounceRunCheerpjRef.current) {
        debounceRunCheerpjRef.current();
      } else {
        runCheerpj();
      }
    }
  }, [runCode, autoRun, setRunCode]);

  return null; // This component doesn't render anything visible
}
