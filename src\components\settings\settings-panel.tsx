'use client';

import { ThemeSwitcher } from './theme-switcher';
import { useReplStore } from '@/lib/stores/repl-store';

export function SettingsPanel() {
  const { autoRun, isRunning, setAutoRun, setRunCode } = useReplStore();

  const handleAutoRunChange = (checked: boolean) => {
    setAutoRun(checked);
    // If autorun is set, force re-run by updating runCode
    if (checked && !isRunning) {
      setRunCode(true);
    }
  };

  return (
    <>
      {/* Triangle pointing above */}
      <svg
        width="16"
        height="8"
        viewBox="0 0 20 10"
        className="ml-auto mr-[44px]"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M0 10L10 0L20 10H0Z" className="fill-stone-200 dark:fill-stone-950" />
      </svg>

      <div className="bg-stone-200 dark:bg-stone-950 w-[300px] max-w-full rounded-md shadow-sm px-4 py-3 text-sm accent-orange-600 dark:accent-orange-400 leading-relaxed">
        <h3 className="font-semibold">Appearance</h3>
        <div className="w-1/3">
          Theme:
          <ThemeSwitcher />
        </div>

        <div className="border-t border-stone-300 dark:border-stone-700 my-3" />

        <h3 className="font-semibold">Behaviour</h3>
        <div className="flex items-center gap-1.5">
          <input 
            type="checkbox" 
            checked={autoRun}
            onChange={(e) => handleAutoRunChange(e.target.checked)}
            id="auto-run" 
          />
          <label htmlFor="auto-run" className="grow">Run code automatically</label>
        </div>
      </div>
    </>
  );
}
