'use client';

import { useRef } from 'react';
import { Icon } from '@iconify/react';
import { useReplStore } from '@/lib/stores/repl-store';
import { Loading } from '@/components/loading';
import { CheerpJ } from './cheerpj';

interface OutputProps {
  isOutputMode?: boolean;
}

export function Output({ isOutputMode = true }: OutputProps) {
  const cjConsoleRef = useRef<HTMLPreElement>(null);
  const { isRunning } = useReplStore();

  const viewInOutputMode = () => {
    if (typeof window !== 'undefined') {
      const url = `/output/${window.location.hash}`;
      window.open(url, "_blank", "noreferrer");
    }
  };

  return (
    <>
      <div className={`w-full h-full ${!isRunning ? 'hidden' : ''}`}>
        <Loading />
      </div>

      <div className={`w-full h-full flex ${isRunning ? 'hidden' : ''}`}>
        <section className="flex flex-col w-1/2">
          <div className="p-3 text-stone-500 text-sm select-none">Console</div>
          <div className="grow relative overflow-auto">
            {/* CheerpJ implicitly looks for a #console to write to */}
            <pre 
              ref={cjConsoleRef} 
              className="font-mono text-sm h-0" 
              id="console" 
            />
          </div>
        </section>
        <section className="flex flex-col w-1/2">
          <div className="p-3 text-stone-500 text-sm select-none">Result</div>
          <div className="grow relative" id="output">
            {/* #cheerpjDisplay will be inserted here */}
          </div>
        </section>
      </div>

      {isOutputMode && (
        <div className="absolute top-1/2 right-0 text-stone-500 text-sm flex items-center select-none">
          <button 
            className="px-2 py-2" 
            title="Open in new tab" 
            onClick={viewInOutputMode}
          >
            <Icon icon="mi:external-link" className="w-5 h-5" />
          </button>
        </div>
      )}

      <style jsx global>{`
        #cheerpjDisplay {
          box-shadow: none;
        }
      `}</style>

      <CheerpJ />
    </>
  );
}
