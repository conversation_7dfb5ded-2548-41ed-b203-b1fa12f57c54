'use client';

import { useEffect, useRef, useCallback } from 'react';
import { basicSetup } from 'codemirror';
import { EditorView, keymap } from '@codemirror/view';
import { Compartment, EditorState } from '@codemirror/state';
import { indentWithTab } from '@codemirror/commands';
import { indentUnit } from '@codemirror/language';
import { lintGutter } from '@codemirror/lint';
import { java } from '@codemirror/lang-java';
import { useReplStore } from '@/lib/stores/repl-store';
import { compartment, diagnostic, parseCompileLog } from '@/lib/repl/linter';
import { useTheme } from 'next-themes';
import { coolGlow, tomorrow } from 'thememirror';
import { compress } from '@/lib/compress-fiddle';
import '@/lib/repl/codemirror.css';

export function Editor() {
  const containerRef = useRef<HTMLDivElement>(null);
  const editorViewRef = useRef<EditorView | undefined>();
  const editorStatesRef = useRef<Map<string, EditorState>>(new Map());
  const themeCompartmentRef = useRef(new Compartment());
  const skipResetRef = useRef(false);
  
  const { theme } = useTheme();
  const {
    files,
    selectedFilePath,
    fiddleTitle,
    fiddleUpdated,
    compileLog,
    setFiles,
    setFiddleTitle,
    setFiddleUpdated,
  } = useReplStore();

  const editorTheme = theme === 'dark' ? coolGlow : tomorrow;

  const extensions = useCallback(() => [
    basicSetup,
    keymap.of([indentWithTab]),
    indentUnit.of('    '),
    lintGutter(),
    compartment.of(diagnostic.of([])),
    themeCompartmentRef.current.of(editorTheme)
  ], [editorTheme]);

  const updateFragmentURL = useCallback(() => {
    const newUpdated = new Date();
    setFiddleUpdated(newUpdated);
    const fiddleFragmentURL = compress({
      title: fiddleTitle,
      updated: newUpdated,
      files: files
    });
    // Use replace so history is not updated for every change
    window.history.replaceState(null, '', `#${fiddleFragmentURL}`);
  }, [fiddleTitle, files, setFiddleUpdated]);

  // Reset editor states when files change
  const resetEditorStates = useCallback(() => {
    if (skipResetRef.current) return;

    for (const file of files) {
      let state = editorStatesRef.current.get(file.path);
      if (state) {
        // Update state to match filesystem
        const existing = state.doc.toString();
        if (file.content !== existing) {
          const transaction = state.update({
            changes: {
              from: 0,
              to: existing.length,
              insert: file.content
            }
          });
          state = transaction.state;
        }
      } else {
        const extension = file.path.split('.').pop();
        state = EditorState.create({
          doc: file.content,
          extensions: extension === 'java' ? [...extensions(), java()] : extensions()
        });
      }

      editorStatesRef.current.set(file.path, state);
      if (file.path === selectedFilePath) {
        editorViewRef.current?.setState(state);
      }
    }
  }, [files, selectedFilePath, extensions]);

  // Initialize editor
  useEffect(() => {
    if (!containerRef.current) return;

    const editorView = new EditorView({
      parent: containerRef.current,
      state: editorStatesRef.current.get(selectedFilePath),
      dispatch: (transaction) => {
        editorView.update([transaction]);
        editorStatesRef.current.set(selectedFilePath, transaction.state);

        if (transaction.docChanged) {
          skipResetRef.current = true;
          const newFiles = files.map((file) => {
            if (file.path === selectedFilePath) {
              return {
                ...file,
                content: transaction.state.doc.toString()
              };
            }
            return file;
          });
          setFiles(newFiles);
          
          // Use setTimeout to allow state update to complete
          setTimeout(() => {
            skipResetRef.current = false;
            updateFragmentURL();
          }, 0);
        }
      }
    });

    editorViewRef.current = editorView;

    return () => {
      editorView.destroy();
    };
  }, []);

  // Reset states when files change
  useEffect(() => {
    resetEditorStates();
  }, [resetEditorStates]);

  // Switch to selected file
  useEffect(() => {
    const state = editorStatesRef.current.get(selectedFilePath);
    if (state && editorViewRef.current) {
      editorViewRef.current.setState(state);
    }
  }, [selectedFilePath]);

  // Update linter diagnostics
  useEffect(() => {
    const diagnostics = parseCompileLog(compileLog, files);
    for (let fileIndex = 0; fileIndex < diagnostics.length; fileIndex++) {
      const diagnosticsForFile = diagnostics[fileIndex];
      const path = files[fileIndex].path;
      const tr = {
        effects: compartment.reconfigure(diagnostic.of(diagnosticsForFile))
      };
      
      if (selectedFilePath === path) {
        editorViewRef.current?.dispatch(tr);
      } else {
        // Update state in place for non-visible files
        const state = editorStatesRef.current.get(path);
        if (state) {
          editorStatesRef.current.set(path, state.update(tr).state);
        }
      }
    }
  }, [compileLog, files, selectedFilePath]);

  // Update theme
  useEffect(() => {
    const tr = {
      effects: themeCompartmentRef.current.reconfigure(editorTheme)
    };

    // Update current file
    editorViewRef.current?.dispatch(tr);
    
    // Update all other files
    for (const [key, value] of editorStatesRef.current.entries()) {
      editorStatesRef.current.set(key, value.update(tr).state);
    }
  }, [editorTheme]);

  return <div ref={containerRef} className="grow overflow-hidden" />;
}
