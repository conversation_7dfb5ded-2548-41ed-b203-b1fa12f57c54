import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { Fiddle } from '../compress-fiddle';

export type File = {
  path: string;
  content: string;
};

interface ReplState {
  // File management
  files: File[];
  selectedFilePath: string;
  setFiles: (files: File[]) => void;
  setSelectedFilePath: (path: string) => void;
  
  // Fiddle metadata
  fiddleTitle: string;
  fiddleUpdated: Date | undefined;
  setFiddleTitle: (title: string) => void;
  setFiddleUpdated: (date: Date | undefined) => void;
  
  // Favorites
  favourites: Fiddle[];
  favouriteIndex: number;
  setFavourites: (favourites: Fiddle[]) => void;
  setFavouriteIndex: (index: number) => void;
  
  // UI state
  isSidebarOpen: boolean;
  setIsSidebarOpen: (open: boolean) => void;
  
  // Execution state
  autoRun: boolean;
  isRunning: boolean;
  isSaved: boolean;
  runCode: boolean;
  compileLog: string;
  setAutoRun: (autoRun: boolean) => void;
  setIsRunning: (running: boolean) => void;
  setIsSaved: (saved: boolean) => void;
  setRunCode: (run: boolean) => void;
  setCompileLog: (log: string) => void;
}

// Create the main repl store
export const useReplStore = create<ReplState>()(
  persist(
    (set, get) => ({
      // File management
      files: [],
      selectedFilePath: 'Main.java',
      setFiles: (files) => set({ files }),
      setSelectedFilePath: (selectedFilePath) => set({ selectedFilePath }),
      
      // Fiddle metadata
      fiddleTitle: '',
      fiddleUpdated: undefined,
      setFiddleTitle: (fiddleTitle) => set({ fiddleTitle }),
      setFiddleUpdated: (fiddleUpdated) => set({ fiddleUpdated }),
      
      // Favorites
      favourites: [],
      favouriteIndex: -1,
      setFavourites: (favourites) => set({ favourites }),
      setFavouriteIndex: (favouriteIndex) => set({ favouriteIndex }),
      
      // UI state
      isSidebarOpen: true,
      setIsSidebarOpen: (isSidebarOpen) => set({ isSidebarOpen }),
      
      // Execution state
      autoRun: false,
      isRunning: false,
      isSaved: true,
      runCode: false,
      compileLog: '',
      setAutoRun: (autoRun) => set({ autoRun }),
      setIsRunning: (isRunning) => set({ isRunning }),
      setIsSaved: (isSaved) => set({ isSaved }),
      setRunCode: (runCode) => set({ runCode }),
      setCompileLog: (compileLog) => set({ compileLog }),
    }),
    {
      name: 'javafiddle-repl-storage',
      storage: createJSONStorage(() => localStorage),
      // Only persist certain values
      partialize: (state) => ({
        isSidebarOpen: state.isSidebarOpen,
        autoRun: state.autoRun,
      }),
    }
  )
);

// Create a separate store for favorites that uses IndexedDB-like storage
export const useFavouritesStore = create<{
  favourites: Fiddle[];
  setFavourites: (favourites: Fiddle[]) => void;
  addFavourite: (fiddle: Fiddle) => void;
  removeFavourite: (index: number) => void;
}>()(
  persist(
    (set, get) => ({
      favourites: [],
      setFavourites: (favourites) => set({ favourites }),
      addFavourite: (fiddle) => set((state) => ({ 
        favourites: [...state.favourites, fiddle] 
      })),
      removeFavourite: (index) => set((state) => ({ 
        favourites: state.favourites.filter((_, i) => i !== index) 
      })),
    }),
    {
      name: 'javafiddle-favourites-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
