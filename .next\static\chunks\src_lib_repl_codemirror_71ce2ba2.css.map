{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/lib/repl/codemirror.css"], "sourcesContent": [".codemirror-wrapper {\n  height: 100%;\n}\n\n.cm-editor {\n  height: 100%;\n}\n\n.cm-editor .cm-content {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',\n    'Courier New', monospace; /* font-mono */\n  font-size: 13px;\n}\n\n.dark .cm-editor,\n.dark .cm-gutters {\n  background-color: rgb(28 25 23 / 1); /* bg-stone-900 */\n}\n\n.cm-gutters {\n  border-right: 0 !important;\n}\n"], "names": [], "mappings": "AAAA;;;;AAQA;;;;;AAMA;;;;AAKA", "debugId": null}}]}