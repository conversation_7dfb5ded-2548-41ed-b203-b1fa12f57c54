'use client';

import { Icon } from '@iconify/react';
import { useTheme } from 'next-themes';

export function ThemeSwitcher() {
  const { theme, setTheme } = useTheme();

  const toggle = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <button
      className="flex items-center gap-1 w-full px-2 py-1 bg-white dark:bg-stone-800 border border-stone-500 rounded"
      onClick={toggle}
      aria-label="Toggle dark theme"
    >
      <Icon 
        icon={theme === 'dark' ? 'mi:moon' : 'mi:sun'} 
        className="w-5 h-5" 
      />
      {theme === 'dark' ? 'Dark' : 'Light'}
    </button>
  );
}
