{"name": "javafiddle", "version": "0.1.0", "private": true, "packageManager": "bun@1.2.19", "scripts": {"dev": "bun --bun next dev --turbopack", "build": "bun --bun next build", "start": "bun --bun next start", "lint": "bun --bun next lint"}, "dependencies": {"@codemirror/commands": "^6.8.1", "@codemirror/lang-java": "^6.0.2", "@codemirror/language": "^6.11.2", "@codemirror/lint": "^6.8.5", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.38.1", "@iconify/react": "^6.0.0", "codemirror": "^6.0.2", "lz-string": "^1.5.0", "next": "15.4.5", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-split-pane": "^0.1.92", "react-timeago": "^8.3.0", "thememirror": "^2.0.1", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.4.5", "@eslint/eslintrc": "^3"}}