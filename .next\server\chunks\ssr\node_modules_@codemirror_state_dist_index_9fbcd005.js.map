{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/node_modules/%40codemirror/state/dist/index.js"], "sourcesContent": ["import { find<PERSON>lusterBreak as findClusterBreak$1 } from '@marijn/find-cluster-break';\n\n/**\nThe data structure for documents. @nonabstract\n*/\nclass Text {\n    /**\n    Get the line description around the given position.\n    */\n    lineAt(pos) {\n        if (pos < 0 || pos > this.length)\n            throw new RangeError(`Invalid position ${pos} in document of length ${this.length}`);\n        return this.lineInner(pos, false, 1, 0);\n    }\n    /**\n    Get the description for the given (1-based) line number.\n    */\n    line(n) {\n        if (n < 1 || n > this.lines)\n            throw new RangeError(`Invalid line number ${n} in ${this.lines}-line document`);\n        return this.lineInner(n, true, 1, 0);\n    }\n    /**\n    Replace a range of the text with the given content.\n    */\n    replace(from, to, text) {\n        [from, to] = clip(this, from, to);\n        let parts = [];\n        this.decompose(0, from, parts, 2 /* Open.To */);\n        if (text.length)\n            text.decompose(0, text.length, parts, 1 /* Open.From */ | 2 /* Open.To */);\n        this.decompose(to, this.length, parts, 1 /* Open.From */);\n        return TextNode.from(parts, this.length - (to - from) + text.length);\n    }\n    /**\n    Append another document to this one.\n    */\n    append(other) {\n        return this.replace(this.length, this.length, other);\n    }\n    /**\n    Retrieve the text between the given points.\n    */\n    slice(from, to = this.length) {\n        [from, to] = clip(this, from, to);\n        let parts = [];\n        this.decompose(from, to, parts, 0);\n        return TextNode.from(parts, to - from);\n    }\n    /**\n    Test whether this text is equal to another instance.\n    */\n    eq(other) {\n        if (other == this)\n            return true;\n        if (other.length != this.length || other.lines != this.lines)\n            return false;\n        let start = this.scanIdentical(other, 1), end = this.length - this.scanIdentical(other, -1);\n        let a = new RawTextCursor(this), b = new RawTextCursor(other);\n        for (let skip = start, pos = start;;) {\n            a.next(skip);\n            b.next(skip);\n            skip = 0;\n            if (a.lineBreak != b.lineBreak || a.done != b.done || a.value != b.value)\n                return false;\n            pos += a.value.length;\n            if (a.done || pos >= end)\n                return true;\n        }\n    }\n    /**\n    Iterate over the text. When `dir` is `-1`, iteration happens\n    from end to start. This will return lines and the breaks between\n    them as separate strings.\n    */\n    iter(dir = 1) { return new RawTextCursor(this, dir); }\n    /**\n    Iterate over a range of the text. When `from` > `to`, the\n    iterator will run in reverse.\n    */\n    iterRange(from, to = this.length) { return new PartialTextCursor(this, from, to); }\n    /**\n    Return a cursor that iterates over the given range of lines,\n    _without_ returning the line breaks between, and yielding empty\n    strings for empty lines.\n    \n    When `from` and `to` are given, they should be 1-based line numbers.\n    */\n    iterLines(from, to) {\n        let inner;\n        if (from == null) {\n            inner = this.iter();\n        }\n        else {\n            if (to == null)\n                to = this.lines + 1;\n            let start = this.line(from).from;\n            inner = this.iterRange(start, Math.max(start, to == this.lines + 1 ? this.length : to <= 1 ? 0 : this.line(to - 1).to));\n        }\n        return new LineCursor(inner);\n    }\n    /**\n    Return the document as a string, using newline characters to\n    separate lines.\n    */\n    toString() { return this.sliceString(0); }\n    /**\n    Convert the document to an array of lines (which can be\n    deserialized again via [`Text.of`](https://codemirror.net/6/docs/ref/#state.Text^of)).\n    */\n    toJSON() {\n        let lines = [];\n        this.flatten(lines);\n        return lines;\n    }\n    /**\n    @internal\n    */\n    constructor() { }\n    /**\n    Create a `Text` instance for the given array of lines.\n    */\n    static of(text) {\n        if (text.length == 0)\n            throw new RangeError(\"A document must have at least one line\");\n        if (text.length == 1 && !text[0])\n            return Text.empty;\n        return text.length <= 32 /* Tree.Branch */ ? new TextLeaf(text) : TextNode.from(TextLeaf.split(text, []));\n    }\n}\n// Leaves store an array of line strings. There are always line breaks\n// between these strings. Leaves are limited in size and have to be\n// contained in TextNode instances for bigger documents.\nclass TextLeaf extends Text {\n    constructor(text, length = textLength(text)) {\n        super();\n        this.text = text;\n        this.length = length;\n    }\n    get lines() { return this.text.length; }\n    get children() { return null; }\n    lineInner(target, isLine, line, offset) {\n        for (let i = 0;; i++) {\n            let string = this.text[i], end = offset + string.length;\n            if ((isLine ? line : end) >= target)\n                return new Line(offset, end, line, string);\n            offset = end + 1;\n            line++;\n        }\n    }\n    decompose(from, to, target, open) {\n        let text = from <= 0 && to >= this.length ? this\n            : new TextLeaf(sliceText(this.text, from, to), Math.min(to, this.length) - Math.max(0, from));\n        if (open & 1 /* Open.From */) {\n            let prev = target.pop();\n            let joined = appendText(text.text, prev.text.slice(), 0, text.length);\n            if (joined.length <= 32 /* Tree.Branch */) {\n                target.push(new TextLeaf(joined, prev.length + text.length));\n            }\n            else {\n                let mid = joined.length >> 1;\n                target.push(new TextLeaf(joined.slice(0, mid)), new TextLeaf(joined.slice(mid)));\n            }\n        }\n        else {\n            target.push(text);\n        }\n    }\n    replace(from, to, text) {\n        if (!(text instanceof TextLeaf))\n            return super.replace(from, to, text);\n        [from, to] = clip(this, from, to);\n        let lines = appendText(this.text, appendText(text.text, sliceText(this.text, 0, from)), to);\n        let newLen = this.length + text.length - (to - from);\n        if (lines.length <= 32 /* Tree.Branch */)\n            return new TextLeaf(lines, newLen);\n        return TextNode.from(TextLeaf.split(lines, []), newLen);\n    }\n    sliceString(from, to = this.length, lineSep = \"\\n\") {\n        [from, to] = clip(this, from, to);\n        let result = \"\";\n        for (let pos = 0, i = 0; pos <= to && i < this.text.length; i++) {\n            let line = this.text[i], end = pos + line.length;\n            if (pos > from && i)\n                result += lineSep;\n            if (from < end && to > pos)\n                result += line.slice(Math.max(0, from - pos), to - pos);\n            pos = end + 1;\n        }\n        return result;\n    }\n    flatten(target) {\n        for (let line of this.text)\n            target.push(line);\n    }\n    scanIdentical() { return 0; }\n    static split(text, target) {\n        let part = [], len = -1;\n        for (let line of text) {\n            part.push(line);\n            len += line.length + 1;\n            if (part.length == 32 /* Tree.Branch */) {\n                target.push(new TextLeaf(part, len));\n                part = [];\n                len = -1;\n            }\n        }\n        if (len > -1)\n            target.push(new TextLeaf(part, len));\n        return target;\n    }\n}\n// Nodes provide the tree structure of the `Text` type. They store a\n// number of other nodes or leaves, taking care to balance themselves\n// on changes. There are implied line breaks _between_ the children of\n// a node (but not before the first or after the last child).\nclass TextNode extends Text {\n    constructor(children, length) {\n        super();\n        this.children = children;\n        this.length = length;\n        this.lines = 0;\n        for (let child of children)\n            this.lines += child.lines;\n    }\n    lineInner(target, isLine, line, offset) {\n        for (let i = 0;; i++) {\n            let child = this.children[i], end = offset + child.length, endLine = line + child.lines - 1;\n            if ((isLine ? endLine : end) >= target)\n                return child.lineInner(target, isLine, line, offset);\n            offset = end + 1;\n            line = endLine + 1;\n        }\n    }\n    decompose(from, to, target, open) {\n        for (let i = 0, pos = 0; pos <= to && i < this.children.length; i++) {\n            let child = this.children[i], end = pos + child.length;\n            if (from <= end && to >= pos) {\n                let childOpen = open & ((pos <= from ? 1 /* Open.From */ : 0) | (end >= to ? 2 /* Open.To */ : 0));\n                if (pos >= from && end <= to && !childOpen)\n                    target.push(child);\n                else\n                    child.decompose(from - pos, to - pos, target, childOpen);\n            }\n            pos = end + 1;\n        }\n    }\n    replace(from, to, text) {\n        [from, to] = clip(this, from, to);\n        if (text.lines < this.lines)\n            for (let i = 0, pos = 0; i < this.children.length; i++) {\n                let child = this.children[i], end = pos + child.length;\n                // Fast path: if the change only affects one child and the\n                // child's size remains in the acceptable range, only update\n                // that child\n                if (from >= pos && to <= end) {\n                    let updated = child.replace(from - pos, to - pos, text);\n                    let totalLines = this.lines - child.lines + updated.lines;\n                    if (updated.lines < (totalLines >> (5 /* Tree.BranchShift */ - 1)) &&\n                        updated.lines > (totalLines >> (5 /* Tree.BranchShift */ + 1))) {\n                        let copy = this.children.slice();\n                        copy[i] = updated;\n                        return new TextNode(copy, this.length - (to - from) + text.length);\n                    }\n                    return super.replace(pos, end, updated);\n                }\n                pos = end + 1;\n            }\n        return super.replace(from, to, text);\n    }\n    sliceString(from, to = this.length, lineSep = \"\\n\") {\n        [from, to] = clip(this, from, to);\n        let result = \"\";\n        for (let i = 0, pos = 0; i < this.children.length && pos <= to; i++) {\n            let child = this.children[i], end = pos + child.length;\n            if (pos > from && i)\n                result += lineSep;\n            if (from < end && to > pos)\n                result += child.sliceString(from - pos, to - pos, lineSep);\n            pos = end + 1;\n        }\n        return result;\n    }\n    flatten(target) {\n        for (let child of this.children)\n            child.flatten(target);\n    }\n    scanIdentical(other, dir) {\n        if (!(other instanceof TextNode))\n            return 0;\n        let length = 0;\n        let [iA, iB, eA, eB] = dir > 0 ? [0, 0, this.children.length, other.children.length]\n            : [this.children.length - 1, other.children.length - 1, -1, -1];\n        for (;; iA += dir, iB += dir) {\n            if (iA == eA || iB == eB)\n                return length;\n            let chA = this.children[iA], chB = other.children[iB];\n            if (chA != chB)\n                return length + chA.scanIdentical(chB, dir);\n            length += chA.length + 1;\n        }\n    }\n    static from(children, length = children.reduce((l, ch) => l + ch.length + 1, -1)) {\n        let lines = 0;\n        for (let ch of children)\n            lines += ch.lines;\n        if (lines < 32 /* Tree.Branch */) {\n            let flat = [];\n            for (let ch of children)\n                ch.flatten(flat);\n            return new TextLeaf(flat, length);\n        }\n        let chunk = Math.max(32 /* Tree.Branch */, lines >> 5 /* Tree.BranchShift */), maxChunk = chunk << 1, minChunk = chunk >> 1;\n        let chunked = [], currentLines = 0, currentLen = -1, currentChunk = [];\n        function add(child) {\n            let last;\n            if (child.lines > maxChunk && child instanceof TextNode) {\n                for (let node of child.children)\n                    add(node);\n            }\n            else if (child.lines > minChunk && (currentLines > minChunk || !currentLines)) {\n                flush();\n                chunked.push(child);\n            }\n            else if (child instanceof TextLeaf && currentLines &&\n                (last = currentChunk[currentChunk.length - 1]) instanceof TextLeaf &&\n                child.lines + last.lines <= 32 /* Tree.Branch */) {\n                currentLines += child.lines;\n                currentLen += child.length + 1;\n                currentChunk[currentChunk.length - 1] = new TextLeaf(last.text.concat(child.text), last.length + 1 + child.length);\n            }\n            else {\n                if (currentLines + child.lines > chunk)\n                    flush();\n                currentLines += child.lines;\n                currentLen += child.length + 1;\n                currentChunk.push(child);\n            }\n        }\n        function flush() {\n            if (currentLines == 0)\n                return;\n            chunked.push(currentChunk.length == 1 ? currentChunk[0] : TextNode.from(currentChunk, currentLen));\n            currentLen = -1;\n            currentLines = currentChunk.length = 0;\n        }\n        for (let child of children)\n            add(child);\n        flush();\n        return chunked.length == 1 ? chunked[0] : new TextNode(chunked, length);\n    }\n}\nText.empty = /*@__PURE__*/new TextLeaf([\"\"], 0);\nfunction textLength(text) {\n    let length = -1;\n    for (let line of text)\n        length += line.length + 1;\n    return length;\n}\nfunction appendText(text, target, from = 0, to = 1e9) {\n    for (let pos = 0, i = 0, first = true; i < text.length && pos <= to; i++) {\n        let line = text[i], end = pos + line.length;\n        if (end >= from) {\n            if (end > to)\n                line = line.slice(0, to - pos);\n            if (pos < from)\n                line = line.slice(from - pos);\n            if (first) {\n                target[target.length - 1] += line;\n                first = false;\n            }\n            else\n                target.push(line);\n        }\n        pos = end + 1;\n    }\n    return target;\n}\nfunction sliceText(text, from, to) {\n    return appendText(text, [\"\"], from, to);\n}\nclass RawTextCursor {\n    constructor(text, dir = 1) {\n        this.dir = dir;\n        this.done = false;\n        this.lineBreak = false;\n        this.value = \"\";\n        this.nodes = [text];\n        this.offsets = [dir > 0 ? 1 : (text instanceof TextLeaf ? text.text.length : text.children.length) << 1];\n    }\n    nextInner(skip, dir) {\n        this.done = this.lineBreak = false;\n        for (;;) {\n            let last = this.nodes.length - 1;\n            let top = this.nodes[last], offsetValue = this.offsets[last], offset = offsetValue >> 1;\n            let size = top instanceof TextLeaf ? top.text.length : top.children.length;\n            if (offset == (dir > 0 ? size : 0)) {\n                if (last == 0) {\n                    this.done = true;\n                    this.value = \"\";\n                    return this;\n                }\n                if (dir > 0)\n                    this.offsets[last - 1]++;\n                this.nodes.pop();\n                this.offsets.pop();\n            }\n            else if ((offsetValue & 1) == (dir > 0 ? 0 : 1)) {\n                this.offsets[last] += dir;\n                if (skip == 0) {\n                    this.lineBreak = true;\n                    this.value = \"\\n\";\n                    return this;\n                }\n                skip--;\n            }\n            else if (top instanceof TextLeaf) {\n                // Move to the next string\n                let next = top.text[offset + (dir < 0 ? -1 : 0)];\n                this.offsets[last] += dir;\n                if (next.length > Math.max(0, skip)) {\n                    this.value = skip == 0 ? next : dir > 0 ? next.slice(skip) : next.slice(0, next.length - skip);\n                    return this;\n                }\n                skip -= next.length;\n            }\n            else {\n                let next = top.children[offset + (dir < 0 ? -1 : 0)];\n                if (skip > next.length) {\n                    skip -= next.length;\n                    this.offsets[last] += dir;\n                }\n                else {\n                    if (dir < 0)\n                        this.offsets[last]--;\n                    this.nodes.push(next);\n                    this.offsets.push(dir > 0 ? 1 : (next instanceof TextLeaf ? next.text.length : next.children.length) << 1);\n                }\n            }\n        }\n    }\n    next(skip = 0) {\n        if (skip < 0) {\n            this.nextInner(-skip, (-this.dir));\n            skip = this.value.length;\n        }\n        return this.nextInner(skip, this.dir);\n    }\n}\nclass PartialTextCursor {\n    constructor(text, start, end) {\n        this.value = \"\";\n        this.done = false;\n        this.cursor = new RawTextCursor(text, start > end ? -1 : 1);\n        this.pos = start > end ? text.length : 0;\n        this.from = Math.min(start, end);\n        this.to = Math.max(start, end);\n    }\n    nextInner(skip, dir) {\n        if (dir < 0 ? this.pos <= this.from : this.pos >= this.to) {\n            this.value = \"\";\n            this.done = true;\n            return this;\n        }\n        skip += Math.max(0, dir < 0 ? this.pos - this.to : this.from - this.pos);\n        let limit = dir < 0 ? this.pos - this.from : this.to - this.pos;\n        if (skip > limit)\n            skip = limit;\n        limit -= skip;\n        let { value } = this.cursor.next(skip);\n        this.pos += (value.length + skip) * dir;\n        this.value = value.length <= limit ? value : dir < 0 ? value.slice(value.length - limit) : value.slice(0, limit);\n        this.done = !this.value;\n        return this;\n    }\n    next(skip = 0) {\n        if (skip < 0)\n            skip = Math.max(skip, this.from - this.pos);\n        else if (skip > 0)\n            skip = Math.min(skip, this.to - this.pos);\n        return this.nextInner(skip, this.cursor.dir);\n    }\n    get lineBreak() { return this.cursor.lineBreak && this.value != \"\"; }\n}\nclass LineCursor {\n    constructor(inner) {\n        this.inner = inner;\n        this.afterBreak = true;\n        this.value = \"\";\n        this.done = false;\n    }\n    next(skip = 0) {\n        let { done, lineBreak, value } = this.inner.next(skip);\n        if (done && this.afterBreak) {\n            this.value = \"\";\n            this.afterBreak = false;\n        }\n        else if (done) {\n            this.done = true;\n            this.value = \"\";\n        }\n        else if (lineBreak) {\n            if (this.afterBreak) {\n                this.value = \"\";\n            }\n            else {\n                this.afterBreak = true;\n                this.next();\n            }\n        }\n        else {\n            this.value = value;\n            this.afterBreak = false;\n        }\n        return this;\n    }\n    get lineBreak() { return false; }\n}\nif (typeof Symbol != \"undefined\") {\n    Text.prototype[Symbol.iterator] = function () { return this.iter(); };\n    RawTextCursor.prototype[Symbol.iterator] = PartialTextCursor.prototype[Symbol.iterator] =\n        LineCursor.prototype[Symbol.iterator] = function () { return this; };\n}\n/**\nThis type describes a line in the document. It is created\non-demand when lines are [queried](https://codemirror.net/6/docs/ref/#state.Text.lineAt).\n*/\nclass Line {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position of the start of the line.\n    */\n    from, \n    /**\n    The position at the end of the line (_before_ the line break,\n    or at the end of document for the last line).\n    */\n    to, \n    /**\n    This line's line number (1-based).\n    */\n    number, \n    /**\n    The line's content.\n    */\n    text) {\n        this.from = from;\n        this.to = to;\n        this.number = number;\n        this.text = text;\n    }\n    /**\n    The length of the line (not including any line break after it).\n    */\n    get length() { return this.to - this.from; }\n}\nfunction clip(text, from, to) {\n    from = Math.max(0, Math.min(text.length, from));\n    return [from, Math.max(from, Math.min(text.length, to))];\n}\n\n/**\nReturns a next grapheme cluster break _after_ (not equal to)\n`pos`, if `forward` is true, or before otherwise. Returns `pos`\nitself if no further cluster break is available in the string.\nMoves across surrogate pairs, extending characters (when\n`includeExtending` is true), characters joined with zero-width\njoiners, and flag emoji.\n*/\nfunction findClusterBreak(str, pos, forward = true, includeExtending = true) {\n    return findClusterBreak$1(str, pos, forward, includeExtending);\n}\nfunction surrogateLow(ch) { return ch >= 0xDC00 && ch < 0xE000; }\nfunction surrogateHigh(ch) { return ch >= 0xD800 && ch < 0xDC00; }\n/**\nFind the code point at the given position in a string (like the\n[`codePointAt`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/codePointAt)\nstring method).\n*/\nfunction codePointAt(str, pos) {\n    let code0 = str.charCodeAt(pos);\n    if (!surrogateHigh(code0) || pos + 1 == str.length)\n        return code0;\n    let code1 = str.charCodeAt(pos + 1);\n    if (!surrogateLow(code1))\n        return code0;\n    return ((code0 - 0xd800) << 10) + (code1 - 0xdc00) + 0x10000;\n}\n/**\nGiven a Unicode codepoint, return the JavaScript string that\nrespresents it (like\n[`String.fromCodePoint`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/fromCodePoint)).\n*/\nfunction fromCodePoint(code) {\n    if (code <= 0xffff)\n        return String.fromCharCode(code);\n    code -= 0x10000;\n    return String.fromCharCode((code >> 10) + 0xd800, (code & 1023) + 0xdc00);\n}\n/**\nThe amount of positions a character takes up in a JavaScript string.\n*/\nfunction codePointSize(code) { return code < 0x10000 ? 1 : 2; }\n\nconst DefaultSplit = /\\r\\n?|\\n/;\n/**\nDistinguishes different ways in which positions can be mapped.\n*/\nvar MapMode = /*@__PURE__*/(function (MapMode) {\n    /**\n    Map a position to a valid new position, even when its context\n    was deleted.\n    */\n    MapMode[MapMode[\"Simple\"] = 0] = \"Simple\";\n    /**\n    Return null if deletion happens across the position.\n    */\n    MapMode[MapMode[\"TrackDel\"] = 1] = \"TrackDel\";\n    /**\n    Return null if the character _before_ the position is deleted.\n    */\n    MapMode[MapMode[\"TrackBefore\"] = 2] = \"TrackBefore\";\n    /**\n    Return null if the character _after_ the position is deleted.\n    */\n    MapMode[MapMode[\"TrackAfter\"] = 3] = \"TrackAfter\";\nreturn MapMode})(MapMode || (MapMode = {}));\n/**\nA change description is a variant of [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet)\nthat doesn't store the inserted text. As such, it can't be\napplied, but is cheaper to store and manipulate.\n*/\nclass ChangeDesc {\n    // Sections are encoded as pairs of integers. The first is the\n    // length in the current document, and the second is -1 for\n    // unaffected sections, and the length of the replacement content\n    // otherwise. So an insertion would be (0, n>0), a deletion (n>0,\n    // 0), and a replacement two positive numbers.\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    sections) {\n        this.sections = sections;\n    }\n    /**\n    The length of the document before the change.\n    */\n    get length() {\n        let result = 0;\n        for (let i = 0; i < this.sections.length; i += 2)\n            result += this.sections[i];\n        return result;\n    }\n    /**\n    The length of the document after the change.\n    */\n    get newLength() {\n        let result = 0;\n        for (let i = 0; i < this.sections.length; i += 2) {\n            let ins = this.sections[i + 1];\n            result += ins < 0 ? this.sections[i] : ins;\n        }\n        return result;\n    }\n    /**\n    False when there are actual changes in this set.\n    */\n    get empty() { return this.sections.length == 0 || this.sections.length == 2 && this.sections[1] < 0; }\n    /**\n    Iterate over the unchanged parts left by these changes. `posA`\n    provides the position of the range in the old document, `posB`\n    the new position in the changed document.\n    */\n    iterGaps(f) {\n        for (let i = 0, posA = 0, posB = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            if (ins < 0) {\n                f(posA, posB, len);\n                posB += len;\n            }\n            else {\n                posB += ins;\n            }\n            posA += len;\n        }\n    }\n    /**\n    Iterate over the ranges changed by these changes. (See\n    [`ChangeSet.iterChanges`](https://codemirror.net/6/docs/ref/#state.ChangeSet.iterChanges) for a\n    variant that also provides you with the inserted text.)\n    `fromA`/`toA` provides the extent of the change in the starting\n    document, `fromB`/`toB` the extent of the replacement in the\n    changed document.\n    \n    When `individual` is true, adjacent changes (which are kept\n    separate for [position mapping](https://codemirror.net/6/docs/ref/#state.ChangeDesc.mapPos)) are\n    reported separately.\n    */\n    iterChangedRanges(f, individual = false) {\n        iterChanges(this, f, individual);\n    }\n    /**\n    Get a description of the inverted form of these changes.\n    */\n    get invertedDesc() {\n        let sections = [];\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            if (ins < 0)\n                sections.push(len, ins);\n            else\n                sections.push(ins, len);\n        }\n        return new ChangeDesc(sections);\n    }\n    /**\n    Compute the combined effect of applying another set of changes\n    after this one. The length of the document after this set should\n    match the length before `other`.\n    */\n    composeDesc(other) { return this.empty ? other : other.empty ? this : composeSets(this, other); }\n    /**\n    Map this description, which should start with the same document\n    as `other`, over another set of changes, so that it can be\n    applied after it. When `before` is true, map as if the changes\n    in `this` happened before the ones in `other`.\n    */\n    mapDesc(other, before = false) { return other.empty ? this : mapSet(this, other, before); }\n    mapPos(pos, assoc = -1, mode = MapMode.Simple) {\n        let posA = 0, posB = 0;\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++], endA = posA + len;\n            if (ins < 0) {\n                if (endA > pos)\n                    return posB + (pos - posA);\n                posB += len;\n            }\n            else {\n                if (mode != MapMode.Simple && endA >= pos &&\n                    (mode == MapMode.TrackDel && posA < pos && endA > pos ||\n                        mode == MapMode.TrackBefore && posA < pos ||\n                        mode == MapMode.TrackAfter && endA > pos))\n                    return null;\n                if (endA > pos || endA == pos && assoc < 0 && !len)\n                    return pos == posA || assoc < 0 ? posB : posB + ins;\n                posB += ins;\n            }\n            posA = endA;\n        }\n        if (pos > posA)\n            throw new RangeError(`Position ${pos} is out of range for changeset of length ${posA}`);\n        return posB;\n    }\n    /**\n    Check whether these changes touch a given range. When one of the\n    changes entirely covers the range, the string `\"cover\"` is\n    returned.\n    */\n    touchesRange(from, to = from) {\n        for (let i = 0, pos = 0; i < this.sections.length && pos <= to;) {\n            let len = this.sections[i++], ins = this.sections[i++], end = pos + len;\n            if (ins >= 0 && pos <= to && end >= from)\n                return pos < from && end > to ? \"cover\" : true;\n            pos = end;\n        }\n        return false;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let result = \"\";\n        for (let i = 0; i < this.sections.length;) {\n            let len = this.sections[i++], ins = this.sections[i++];\n            result += (result ? \" \" : \"\") + len + (ins >= 0 ? \":\" + ins : \"\");\n        }\n        return result;\n    }\n    /**\n    Serialize this change desc to a JSON-representable value.\n    */\n    toJSON() { return this.sections; }\n    /**\n    Create a change desc from its JSON representation (as produced\n    by [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeDesc.toJSON).\n    */\n    static fromJSON(json) {\n        if (!Array.isArray(json) || json.length % 2 || json.some(a => typeof a != \"number\"))\n            throw new RangeError(\"Invalid JSON representation of ChangeDesc\");\n        return new ChangeDesc(json);\n    }\n    /**\n    @internal\n    */\n    static create(sections) { return new ChangeDesc(sections); }\n}\n/**\nA change set represents a group of modifications to a document. It\nstores the document length, and can only be applied to documents\nwith exactly that length.\n*/\nclass ChangeSet extends ChangeDesc {\n    constructor(sections, \n    /**\n    @internal\n    */\n    inserted) {\n        super(sections);\n        this.inserted = inserted;\n    }\n    /**\n    Apply the changes to a document, returning the modified\n    document.\n    */\n    apply(doc) {\n        if (this.length != doc.length)\n            throw new RangeError(\"Applying change set to a document with the wrong length\");\n        iterChanges(this, (fromA, toA, fromB, _toB, text) => doc = doc.replace(fromB, fromB + (toA - fromA), text), false);\n        return doc;\n    }\n    mapDesc(other, before = false) { return mapSet(this, other, before, true); }\n    /**\n    Given the document as it existed _before_ the changes, return a\n    change set that represents the inverse of this set, which could\n    be used to go from the document created by the changes back to\n    the document as it existed before the changes.\n    */\n    invert(doc) {\n        let sections = this.sections.slice(), inserted = [];\n        for (let i = 0, pos = 0; i < sections.length; i += 2) {\n            let len = sections[i], ins = sections[i + 1];\n            if (ins >= 0) {\n                sections[i] = ins;\n                sections[i + 1] = len;\n                let index = i >> 1;\n                while (inserted.length < index)\n                    inserted.push(Text.empty);\n                inserted.push(len ? doc.slice(pos, pos + len) : Text.empty);\n            }\n            pos += len;\n        }\n        return new ChangeSet(sections, inserted);\n    }\n    /**\n    Combine two subsequent change sets into a single set. `other`\n    must start in the document produced by `this`. If `this` goes\n    `docA` → `docB` and `other` represents `docB` → `docC`, the\n    returned value will represent the change `docA` → `docC`.\n    */\n    compose(other) { return this.empty ? other : other.empty ? this : composeSets(this, other, true); }\n    /**\n    Given another change set starting in the same document, maps this\n    change set over the other, producing a new change set that can be\n    applied to the document produced by applying `other`. When\n    `before` is `true`, order changes as if `this` comes before\n    `other`, otherwise (the default) treat `other` as coming first.\n    \n    Given two changes `A` and `B`, `A.compose(B.map(A))` and\n    `B.compose(A.map(B, true))` will produce the same document. This\n    provides a basic form of [operational\n    transformation](https://en.wikipedia.org/wiki/Operational_transformation),\n    and can be used for collaborative editing.\n    */\n    map(other, before = false) { return other.empty ? this : mapSet(this, other, before, true); }\n    /**\n    Iterate over the changed ranges in the document, calling `f` for\n    each, with the range in the original document (`fromA`-`toA`)\n    and the range that replaces it in the new document\n    (`fromB`-`toB`).\n    \n    When `individual` is true, adjacent changes are reported\n    separately.\n    */\n    iterChanges(f, individual = false) {\n        iterChanges(this, f, individual);\n    }\n    /**\n    Get a [change description](https://codemirror.net/6/docs/ref/#state.ChangeDesc) for this change\n    set.\n    */\n    get desc() { return ChangeDesc.create(this.sections); }\n    /**\n    @internal\n    */\n    filter(ranges) {\n        let resultSections = [], resultInserted = [], filteredSections = [];\n        let iter = new SectionIter(this);\n        done: for (let i = 0, pos = 0;;) {\n            let next = i == ranges.length ? 1e9 : ranges[i++];\n            while (pos < next || pos == next && iter.len == 0) {\n                if (iter.done)\n                    break done;\n                let len = Math.min(iter.len, next - pos);\n                addSection(filteredSections, len, -1);\n                let ins = iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0;\n                addSection(resultSections, len, ins);\n                if (ins > 0)\n                    addInsert(resultInserted, resultSections, iter.text);\n                iter.forward(len);\n                pos += len;\n            }\n            let end = ranges[i++];\n            while (pos < end) {\n                if (iter.done)\n                    break done;\n                let len = Math.min(iter.len, end - pos);\n                addSection(resultSections, len, -1);\n                addSection(filteredSections, len, iter.ins == -1 ? -1 : iter.off == 0 ? iter.ins : 0);\n                iter.forward(len);\n                pos += len;\n            }\n        }\n        return { changes: new ChangeSet(resultSections, resultInserted),\n            filtered: ChangeDesc.create(filteredSections) };\n    }\n    /**\n    Serialize this change set to a JSON-representable value.\n    */\n    toJSON() {\n        let parts = [];\n        for (let i = 0; i < this.sections.length; i += 2) {\n            let len = this.sections[i], ins = this.sections[i + 1];\n            if (ins < 0)\n                parts.push(len);\n            else if (ins == 0)\n                parts.push([len]);\n            else\n                parts.push([len].concat(this.inserted[i >> 1].toJSON()));\n        }\n        return parts;\n    }\n    /**\n    Create a change set for the given changes, for a document of the\n    given length, using `lineSep` as line separator.\n    */\n    static of(changes, length, lineSep) {\n        let sections = [], inserted = [], pos = 0;\n        let total = null;\n        function flush(force = false) {\n            if (!force && !sections.length)\n                return;\n            if (pos < length)\n                addSection(sections, length - pos, -1);\n            let set = new ChangeSet(sections, inserted);\n            total = total ? total.compose(set.map(total)) : set;\n            sections = [];\n            inserted = [];\n            pos = 0;\n        }\n        function process(spec) {\n            if (Array.isArray(spec)) {\n                for (let sub of spec)\n                    process(sub);\n            }\n            else if (spec instanceof ChangeSet) {\n                if (spec.length != length)\n                    throw new RangeError(`Mismatched change set length (got ${spec.length}, expected ${length})`);\n                flush();\n                total = total ? total.compose(spec.map(total)) : spec;\n            }\n            else {\n                let { from, to = from, insert } = spec;\n                if (from > to || from < 0 || to > length)\n                    throw new RangeError(`Invalid change range ${from} to ${to} (in doc of length ${length})`);\n                let insText = !insert ? Text.empty : typeof insert == \"string\" ? Text.of(insert.split(lineSep || DefaultSplit)) : insert;\n                let insLen = insText.length;\n                if (from == to && insLen == 0)\n                    return;\n                if (from < pos)\n                    flush();\n                if (from > pos)\n                    addSection(sections, from - pos, -1);\n                addSection(sections, to - from, insLen);\n                addInsert(inserted, sections, insText);\n                pos = to;\n            }\n        }\n        process(changes);\n        flush(!total);\n        return total;\n    }\n    /**\n    Create an empty changeset of the given length.\n    */\n    static empty(length) {\n        return new ChangeSet(length ? [length, -1] : [], []);\n    }\n    /**\n    Create a changeset from its JSON representation (as produced by\n    [`toJSON`](https://codemirror.net/6/docs/ref/#state.ChangeSet.toJSON).\n    */\n    static fromJSON(json) {\n        if (!Array.isArray(json))\n            throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n        let sections = [], inserted = [];\n        for (let i = 0; i < json.length; i++) {\n            let part = json[i];\n            if (typeof part == \"number\") {\n                sections.push(part, -1);\n            }\n            else if (!Array.isArray(part) || typeof part[0] != \"number\" || part.some((e, i) => i && typeof e != \"string\")) {\n                throw new RangeError(\"Invalid JSON representation of ChangeSet\");\n            }\n            else if (part.length == 1) {\n                sections.push(part[0], 0);\n            }\n            else {\n                while (inserted.length < i)\n                    inserted.push(Text.empty);\n                inserted[i] = Text.of(part.slice(1));\n                sections.push(part[0], inserted[i].length);\n            }\n        }\n        return new ChangeSet(sections, inserted);\n    }\n    /**\n    @internal\n    */\n    static createSet(sections, inserted) {\n        return new ChangeSet(sections, inserted);\n    }\n}\nfunction addSection(sections, len, ins, forceJoin = false) {\n    if (len == 0 && ins <= 0)\n        return;\n    let last = sections.length - 2;\n    if (last >= 0 && ins <= 0 && ins == sections[last + 1])\n        sections[last] += len;\n    else if (last >= 0 && len == 0 && sections[last] == 0)\n        sections[last + 1] += ins;\n    else if (forceJoin) {\n        sections[last] += len;\n        sections[last + 1] += ins;\n    }\n    else\n        sections.push(len, ins);\n}\nfunction addInsert(values, sections, value) {\n    if (value.length == 0)\n        return;\n    let index = (sections.length - 2) >> 1;\n    if (index < values.length) {\n        values[values.length - 1] = values[values.length - 1].append(value);\n    }\n    else {\n        while (values.length < index)\n            values.push(Text.empty);\n        values.push(value);\n    }\n}\nfunction iterChanges(desc, f, individual) {\n    let inserted = desc.inserted;\n    for (let posA = 0, posB = 0, i = 0; i < desc.sections.length;) {\n        let len = desc.sections[i++], ins = desc.sections[i++];\n        if (ins < 0) {\n            posA += len;\n            posB += len;\n        }\n        else {\n            let endA = posA, endB = posB, text = Text.empty;\n            for (;;) {\n                endA += len;\n                endB += ins;\n                if (ins && inserted)\n                    text = text.append(inserted[(i - 2) >> 1]);\n                if (individual || i == desc.sections.length || desc.sections[i + 1] < 0)\n                    break;\n                len = desc.sections[i++];\n                ins = desc.sections[i++];\n            }\n            f(posA, endA, posB, endB, text);\n            posA = endA;\n            posB = endB;\n        }\n    }\n}\nfunction mapSet(setA, setB, before, mkSet = false) {\n    // Produce a copy of setA that applies to the document after setB\n    // has been applied (assuming both start at the same document).\n    let sections = [], insert = mkSet ? [] : null;\n    let a = new SectionIter(setA), b = new SectionIter(setB);\n    // Iterate over both sets in parallel. inserted tracks, for changes\n    // in A that have to be processed piece-by-piece, whether their\n    // content has been inserted already, and refers to the section\n    // index.\n    for (let inserted = -1;;) {\n        if (a.done && b.len || b.done && a.len) {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n        else if (a.ins == -1 && b.ins == -1) {\n            // Move across ranges skipped by both sets.\n            let len = Math.min(a.len, b.len);\n            addSection(sections, len, -1);\n            a.forward(len);\n            b.forward(len);\n        }\n        else if (b.ins >= 0 && (a.ins < 0 || inserted == a.i || a.off == 0 && (b.len < a.len || b.len == a.len && !before))) {\n            // If there's a change in B that comes before the next change in\n            // A (ordered by start pos, then len, then before flag), skip\n            // that (and process any changes in A it covers).\n            let len = b.len;\n            addSection(sections, b.ins, -1);\n            while (len) {\n                let piece = Math.min(a.len, len);\n                if (a.ins >= 0 && inserted < a.i && a.len <= piece) {\n                    addSection(sections, 0, a.ins);\n                    if (insert)\n                        addInsert(insert, sections, a.text);\n                    inserted = a.i;\n                }\n                a.forward(piece);\n                len -= piece;\n            }\n            b.next();\n        }\n        else if (a.ins >= 0) {\n            // Process the part of a change in A up to the start of the next\n            // non-deletion change in B (if overlapping).\n            let len = 0, left = a.len;\n            while (left) {\n                if (b.ins == -1) {\n                    let piece = Math.min(left, b.len);\n                    len += piece;\n                    left -= piece;\n                    b.forward(piece);\n                }\n                else if (b.ins == 0 && b.len < left) {\n                    left -= b.len;\n                    b.next();\n                }\n                else {\n                    break;\n                }\n            }\n            addSection(sections, len, inserted < a.i ? a.ins : 0);\n            if (insert && inserted < a.i)\n                addInsert(insert, sections, a.text);\n            inserted = a.i;\n            a.forward(a.len - left);\n        }\n        else if (a.done && b.done) {\n            return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n        }\n        else {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n    }\n}\nfunction composeSets(setA, setB, mkSet = false) {\n    let sections = [];\n    let insert = mkSet ? [] : null;\n    let a = new SectionIter(setA), b = new SectionIter(setB);\n    for (let open = false;;) {\n        if (a.done && b.done) {\n            return insert ? ChangeSet.createSet(sections, insert) : ChangeDesc.create(sections);\n        }\n        else if (a.ins == 0) { // Deletion in A\n            addSection(sections, a.len, 0, open);\n            a.next();\n        }\n        else if (b.len == 0 && !b.done) { // Insertion in B\n            addSection(sections, 0, b.ins, open);\n            if (insert)\n                addInsert(insert, sections, b.text);\n            b.next();\n        }\n        else if (a.done || b.done) {\n            throw new Error(\"Mismatched change set lengths\");\n        }\n        else {\n            let len = Math.min(a.len2, b.len), sectionLen = sections.length;\n            if (a.ins == -1) {\n                let insB = b.ins == -1 ? -1 : b.off ? 0 : b.ins;\n                addSection(sections, len, insB, open);\n                if (insert && insB)\n                    addInsert(insert, sections, b.text);\n            }\n            else if (b.ins == -1) {\n                addSection(sections, a.off ? 0 : a.len, len, open);\n                if (insert)\n                    addInsert(insert, sections, a.textBit(len));\n            }\n            else {\n                addSection(sections, a.off ? 0 : a.len, b.off ? 0 : b.ins, open);\n                if (insert && !b.off)\n                    addInsert(insert, sections, b.text);\n            }\n            open = (a.ins > len || b.ins >= 0 && b.len > len) && (open || sections.length > sectionLen);\n            a.forward2(len);\n            b.forward(len);\n        }\n    }\n}\nclass SectionIter {\n    constructor(set) {\n        this.set = set;\n        this.i = 0;\n        this.next();\n    }\n    next() {\n        let { sections } = this.set;\n        if (this.i < sections.length) {\n            this.len = sections[this.i++];\n            this.ins = sections[this.i++];\n        }\n        else {\n            this.len = 0;\n            this.ins = -2;\n        }\n        this.off = 0;\n    }\n    get done() { return this.ins == -2; }\n    get len2() { return this.ins < 0 ? this.len : this.ins; }\n    get text() {\n        let { inserted } = this.set, index = (this.i - 2) >> 1;\n        return index >= inserted.length ? Text.empty : inserted[index];\n    }\n    textBit(len) {\n        let { inserted } = this.set, index = (this.i - 2) >> 1;\n        return index >= inserted.length && !len ? Text.empty\n            : inserted[index].slice(this.off, len == null ? undefined : this.off + len);\n    }\n    forward(len) {\n        if (len == this.len)\n            this.next();\n        else {\n            this.len -= len;\n            this.off += len;\n        }\n    }\n    forward2(len) {\n        if (this.ins == -1)\n            this.forward(len);\n        else if (len == this.ins)\n            this.next();\n        else {\n            this.ins -= len;\n            this.off += len;\n        }\n    }\n}\n\n/**\nA single selection range. When\n[`allowMultipleSelections`](https://codemirror.net/6/docs/ref/#state.EditorState^allowMultipleSelections)\nis enabled, a [selection](https://codemirror.net/6/docs/ref/#state.EditorSelection) may hold\nmultiple ranges. By default, selections hold exactly one range.\n*/\nclass SelectionRange {\n    constructor(\n    /**\n    The lower boundary of the range.\n    */\n    from, \n    /**\n    The upper boundary of the range.\n    */\n    to, flags) {\n        this.from = from;\n        this.to = to;\n        this.flags = flags;\n    }\n    /**\n    The anchor of the range—the side that doesn't move when you\n    extend it.\n    */\n    get anchor() { return this.flags & 32 /* RangeFlag.Inverted */ ? this.to : this.from; }\n    /**\n    The head of the range, which is moved when the range is\n    [extended](https://codemirror.net/6/docs/ref/#state.SelectionRange.extend).\n    */\n    get head() { return this.flags & 32 /* RangeFlag.Inverted */ ? this.from : this.to; }\n    /**\n    True when `anchor` and `head` are at the same position.\n    */\n    get empty() { return this.from == this.to; }\n    /**\n    If this is a cursor that is explicitly associated with the\n    character on one of its sides, this returns the side. -1 means\n    the character before its position, 1 the character after, and 0\n    means no association.\n    */\n    get assoc() { return this.flags & 8 /* RangeFlag.AssocBefore */ ? -1 : this.flags & 16 /* RangeFlag.AssocAfter */ ? 1 : 0; }\n    /**\n    The bidirectional text level associated with this cursor, if\n    any.\n    */\n    get bidiLevel() {\n        let level = this.flags & 7 /* RangeFlag.BidiLevelMask */;\n        return level == 7 ? null : level;\n    }\n    /**\n    The goal column (stored vertical offset) associated with a\n    cursor. This is used to preserve the vertical position when\n    [moving](https://codemirror.net/6/docs/ref/#view.EditorView.moveVertically) across\n    lines of different length.\n    */\n    get goalColumn() {\n        let value = this.flags >> 6 /* RangeFlag.GoalColumnOffset */;\n        return value == 16777215 /* RangeFlag.NoGoalColumn */ ? undefined : value;\n    }\n    /**\n    Map this range through a change, producing a valid range in the\n    updated document.\n    */\n    map(change, assoc = -1) {\n        let from, to;\n        if (this.empty) {\n            from = to = change.mapPos(this.from, assoc);\n        }\n        else {\n            from = change.mapPos(this.from, 1);\n            to = change.mapPos(this.to, -1);\n        }\n        return from == this.from && to == this.to ? this : new SelectionRange(from, to, this.flags);\n    }\n    /**\n    Extend this range to cover at least `from` to `to`.\n    */\n    extend(from, to = from) {\n        if (from <= this.anchor && to >= this.anchor)\n            return EditorSelection.range(from, to);\n        let head = Math.abs(from - this.anchor) > Math.abs(to - this.anchor) ? from : to;\n        return EditorSelection.range(this.anchor, head);\n    }\n    /**\n    Compare this range to another range.\n    */\n    eq(other, includeAssoc = false) {\n        return this.anchor == other.anchor && this.head == other.head &&\n            (!includeAssoc || !this.empty || this.assoc == other.assoc);\n    }\n    /**\n    Return a JSON-serializable object representing the range.\n    */\n    toJSON() { return { anchor: this.anchor, head: this.head }; }\n    /**\n    Convert a JSON representation of a range to a `SelectionRange`\n    instance.\n    */\n    static fromJSON(json) {\n        if (!json || typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid JSON representation for SelectionRange\");\n        return EditorSelection.range(json.anchor, json.head);\n    }\n    /**\n    @internal\n    */\n    static create(from, to, flags) {\n        return new SelectionRange(from, to, flags);\n    }\n}\n/**\nAn editor selection holds one or more selection ranges.\n*/\nclass EditorSelection {\n    constructor(\n    /**\n    The ranges in the selection, sorted by position. Ranges cannot\n    overlap (but they may touch, if they aren't empty).\n    */\n    ranges, \n    /**\n    The index of the _main_ range in the selection (which is\n    usually the range that was added last).\n    */\n    mainIndex) {\n        this.ranges = ranges;\n        this.mainIndex = mainIndex;\n    }\n    /**\n    Map a selection through a change. Used to adjust the selection\n    position for changes.\n    */\n    map(change, assoc = -1) {\n        if (change.empty)\n            return this;\n        return EditorSelection.create(this.ranges.map(r => r.map(change, assoc)), this.mainIndex);\n    }\n    /**\n    Compare this selection to another selection. By default, ranges\n    are compared only by position. When `includeAssoc` is true,\n    cursor ranges must also have the same\n    [`assoc`](https://codemirror.net/6/docs/ref/#state.SelectionRange.assoc) value.\n    */\n    eq(other, includeAssoc = false) {\n        if (this.ranges.length != other.ranges.length ||\n            this.mainIndex != other.mainIndex)\n            return false;\n        for (let i = 0; i < this.ranges.length; i++)\n            if (!this.ranges[i].eq(other.ranges[i], includeAssoc))\n                return false;\n        return true;\n    }\n    /**\n    Get the primary selection range. Usually, you should make sure\n    your code applies to _all_ ranges, by using methods like\n    [`changeByRange`](https://codemirror.net/6/docs/ref/#state.EditorState.changeByRange).\n    */\n    get main() { return this.ranges[this.mainIndex]; }\n    /**\n    Make sure the selection only has one range. Returns a selection\n    holding only the main range from this selection.\n    */\n    asSingle() {\n        return this.ranges.length == 1 ? this : new EditorSelection([this.main], 0);\n    }\n    /**\n    Extend this selection with an extra range.\n    */\n    addRange(range, main = true) {\n        return EditorSelection.create([range].concat(this.ranges), main ? 0 : this.mainIndex + 1);\n    }\n    /**\n    Replace a given range with another range, and then normalize the\n    selection to merge and sort ranges if necessary.\n    */\n    replaceRange(range, which = this.mainIndex) {\n        let ranges = this.ranges.slice();\n        ranges[which] = range;\n        return EditorSelection.create(ranges, this.mainIndex);\n    }\n    /**\n    Convert this selection to an object that can be serialized to\n    JSON.\n    */\n    toJSON() {\n        return { ranges: this.ranges.map(r => r.toJSON()), main: this.mainIndex };\n    }\n    /**\n    Create a selection from a JSON representation.\n    */\n    static fromJSON(json) {\n        if (!json || !Array.isArray(json.ranges) || typeof json.main != \"number\" || json.main >= json.ranges.length)\n            throw new RangeError(\"Invalid JSON representation for EditorSelection\");\n        return new EditorSelection(json.ranges.map((r) => SelectionRange.fromJSON(r)), json.main);\n    }\n    /**\n    Create a selection holding a single range.\n    */\n    static single(anchor, head = anchor) {\n        return new EditorSelection([EditorSelection.range(anchor, head)], 0);\n    }\n    /**\n    Sort and merge the given set of ranges, creating a valid\n    selection.\n    */\n    static create(ranges, mainIndex = 0) {\n        if (ranges.length == 0)\n            throw new RangeError(\"A selection needs at least one range\");\n        for (let pos = 0, i = 0; i < ranges.length; i++) {\n            let range = ranges[i];\n            if (range.empty ? range.from <= pos : range.from < pos)\n                return EditorSelection.normalized(ranges.slice(), mainIndex);\n            pos = range.to;\n        }\n        return new EditorSelection(ranges, mainIndex);\n    }\n    /**\n    Create a cursor selection range at the given position. You can\n    safely ignore the optional arguments in most situations.\n    */\n    static cursor(pos, assoc = 0, bidiLevel, goalColumn) {\n        return SelectionRange.create(pos, pos, (assoc == 0 ? 0 : assoc < 0 ? 8 /* RangeFlag.AssocBefore */ : 16 /* RangeFlag.AssocAfter */) |\n            (bidiLevel == null ? 7 : Math.min(6, bidiLevel)) |\n            ((goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */));\n    }\n    /**\n    Create a selection range.\n    */\n    static range(anchor, head, goalColumn, bidiLevel) {\n        let flags = ((goalColumn !== null && goalColumn !== void 0 ? goalColumn : 16777215 /* RangeFlag.NoGoalColumn */) << 6 /* RangeFlag.GoalColumnOffset */) |\n            (bidiLevel == null ? 7 : Math.min(6, bidiLevel));\n        return head < anchor ? SelectionRange.create(head, anchor, 32 /* RangeFlag.Inverted */ | 16 /* RangeFlag.AssocAfter */ | flags)\n            : SelectionRange.create(anchor, head, (head > anchor ? 8 /* RangeFlag.AssocBefore */ : 0) | flags);\n    }\n    /**\n    @internal\n    */\n    static normalized(ranges, mainIndex = 0) {\n        let main = ranges[mainIndex];\n        ranges.sort((a, b) => a.from - b.from);\n        mainIndex = ranges.indexOf(main);\n        for (let i = 1; i < ranges.length; i++) {\n            let range = ranges[i], prev = ranges[i - 1];\n            if (range.empty ? range.from <= prev.to : range.from < prev.to) {\n                let from = prev.from, to = Math.max(range.to, prev.to);\n                if (i <= mainIndex)\n                    mainIndex--;\n                ranges.splice(--i, 2, range.anchor > range.head ? EditorSelection.range(to, from) : EditorSelection.range(from, to));\n            }\n        }\n        return new EditorSelection(ranges, mainIndex);\n    }\n}\nfunction checkSelection(selection, docLength) {\n    for (let range of selection.ranges)\n        if (range.to > docLength)\n            throw new RangeError(\"Selection points outside of document\");\n}\n\nlet nextID = 0;\n/**\nA facet is a labeled value that is associated with an editor\nstate. It takes inputs from any number of extensions, and combines\nthose into a single output value.\n\nExamples of uses of facets are the [tab\nsize](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize), [editor\nattributes](https://codemirror.net/6/docs/ref/#view.EditorView^editorAttributes), and [update\nlisteners](https://codemirror.net/6/docs/ref/#view.EditorView^updateListener).\n\nNote that `Facet` instances can be used anywhere where\n[`FacetReader`](https://codemirror.net/6/docs/ref/#state.FacetReader) is expected.\n*/\nclass Facet {\n    constructor(\n    /**\n    @internal\n    */\n    combine, \n    /**\n    @internal\n    */\n    compareInput, \n    /**\n    @internal\n    */\n    compare, isStatic, enables) {\n        this.combine = combine;\n        this.compareInput = compareInput;\n        this.compare = compare;\n        this.isStatic = isStatic;\n        /**\n        @internal\n        */\n        this.id = nextID++;\n        this.default = combine([]);\n        this.extensions = typeof enables == \"function\" ? enables(this) : enables;\n    }\n    /**\n    Returns a facet reader for this facet, which can be used to\n    [read](https://codemirror.net/6/docs/ref/#state.EditorState.facet) it but not to define values for it.\n    */\n    get reader() { return this; }\n    /**\n    Define a new facet.\n    */\n    static define(config = {}) {\n        return new Facet(config.combine || ((a) => a), config.compareInput || ((a, b) => a === b), config.compare || (!config.combine ? sameArray : (a, b) => a === b), !!config.static, config.enables);\n    }\n    /**\n    Returns an extension that adds the given value to this facet.\n    */\n    of(value) {\n        return new FacetProvider([], this, 0 /* Provider.Static */, value);\n    }\n    /**\n    Create an extension that computes a value for the facet from a\n    state. You must take care to declare the parts of the state that\n    this value depends on, since your function is only called again\n    for a new state when one of those parts changed.\n    \n    In cases where your value depends only on a single field, you'll\n    want to use the [`from`](https://codemirror.net/6/docs/ref/#state.Facet.from) method instead.\n    */\n    compute(deps, get) {\n        if (this.isStatic)\n            throw new Error(\"Can't compute a static facet\");\n        return new FacetProvider(deps, this, 1 /* Provider.Single */, get);\n    }\n    /**\n    Create an extension that computes zero or more values for this\n    facet from a state.\n    */\n    computeN(deps, get) {\n        if (this.isStatic)\n            throw new Error(\"Can't compute a static facet\");\n        return new FacetProvider(deps, this, 2 /* Provider.Multi */, get);\n    }\n    from(field, get) {\n        if (!get)\n            get = x => x;\n        return this.compute([field], state => get(state.field(field)));\n    }\n}\nfunction sameArray(a, b) {\n    return a == b || a.length == b.length && a.every((e, i) => e === b[i]);\n}\nclass FacetProvider {\n    constructor(dependencies, facet, type, value) {\n        this.dependencies = dependencies;\n        this.facet = facet;\n        this.type = type;\n        this.value = value;\n        this.id = nextID++;\n    }\n    dynamicSlot(addresses) {\n        var _a;\n        let getter = this.value;\n        let compare = this.facet.compareInput;\n        let id = this.id, idx = addresses[id] >> 1, multi = this.type == 2 /* Provider.Multi */;\n        let depDoc = false, depSel = false, depAddrs = [];\n        for (let dep of this.dependencies) {\n            if (dep == \"doc\")\n                depDoc = true;\n            else if (dep == \"selection\")\n                depSel = true;\n            else if ((((_a = addresses[dep.id]) !== null && _a !== void 0 ? _a : 1) & 1) == 0)\n                depAddrs.push(addresses[dep.id]);\n        }\n        return {\n            create(state) {\n                state.values[idx] = getter(state);\n                return 1 /* SlotStatus.Changed */;\n            },\n            update(state, tr) {\n                if ((depDoc && tr.docChanged) || (depSel && (tr.docChanged || tr.selection)) || ensureAll(state, depAddrs)) {\n                    let newVal = getter(state);\n                    if (multi ? !compareArray(newVal, state.values[idx], compare) : !compare(newVal, state.values[idx])) {\n                        state.values[idx] = newVal;\n                        return 1 /* SlotStatus.Changed */;\n                    }\n                }\n                return 0;\n            },\n            reconfigure: (state, oldState) => {\n                let newVal, oldAddr = oldState.config.address[id];\n                if (oldAddr != null) {\n                    let oldVal = getAddr(oldState, oldAddr);\n                    if (this.dependencies.every(dep => {\n                        return dep instanceof Facet ? oldState.facet(dep) === state.facet(dep) :\n                            dep instanceof StateField ? oldState.field(dep, false) == state.field(dep, false) : true;\n                    }) || (multi ? compareArray(newVal = getter(state), oldVal, compare) : compare(newVal = getter(state), oldVal))) {\n                        state.values[idx] = oldVal;\n                        return 0;\n                    }\n                }\n                else {\n                    newVal = getter(state);\n                }\n                state.values[idx] = newVal;\n                return 1 /* SlotStatus.Changed */;\n            }\n        };\n    }\n}\nfunction compareArray(a, b, compare) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (!compare(a[i], b[i]))\n            return false;\n    return true;\n}\nfunction ensureAll(state, addrs) {\n    let changed = false;\n    for (let addr of addrs)\n        if (ensureAddr(state, addr) & 1 /* SlotStatus.Changed */)\n            changed = true;\n    return changed;\n}\nfunction dynamicFacetSlot(addresses, facet, providers) {\n    let providerAddrs = providers.map(p => addresses[p.id]);\n    let providerTypes = providers.map(p => p.type);\n    let dynamic = providerAddrs.filter(p => !(p & 1));\n    let idx = addresses[facet.id] >> 1;\n    function get(state) {\n        let values = [];\n        for (let i = 0; i < providerAddrs.length; i++) {\n            let value = getAddr(state, providerAddrs[i]);\n            if (providerTypes[i] == 2 /* Provider.Multi */)\n                for (let val of value)\n                    values.push(val);\n            else\n                values.push(value);\n        }\n        return facet.combine(values);\n    }\n    return {\n        create(state) {\n            for (let addr of providerAddrs)\n                ensureAddr(state, addr);\n            state.values[idx] = get(state);\n            return 1 /* SlotStatus.Changed */;\n        },\n        update(state, tr) {\n            if (!ensureAll(state, dynamic))\n                return 0;\n            let value = get(state);\n            if (facet.compare(value, state.values[idx]))\n                return 0;\n            state.values[idx] = value;\n            return 1 /* SlotStatus.Changed */;\n        },\n        reconfigure(state, oldState) {\n            let depChanged = ensureAll(state, providerAddrs);\n            let oldProviders = oldState.config.facets[facet.id], oldValue = oldState.facet(facet);\n            if (oldProviders && !depChanged && sameArray(providers, oldProviders)) {\n                state.values[idx] = oldValue;\n                return 0;\n            }\n            let value = get(state);\n            if (facet.compare(value, oldValue)) {\n                state.values[idx] = oldValue;\n                return 0;\n            }\n            state.values[idx] = value;\n            return 1 /* SlotStatus.Changed */;\n        }\n    };\n}\nconst initField = /*@__PURE__*/Facet.define({ static: true });\n/**\nFields can store additional information in an editor state, and\nkeep it in sync with the rest of the state.\n*/\nclass StateField {\n    constructor(\n    /**\n    @internal\n    */\n    id, createF, updateF, compareF, \n    /**\n    @internal\n    */\n    spec) {\n        this.id = id;\n        this.createF = createF;\n        this.updateF = updateF;\n        this.compareF = compareF;\n        this.spec = spec;\n        /**\n        @internal\n        */\n        this.provides = undefined;\n    }\n    /**\n    Define a state field.\n    */\n    static define(config) {\n        let field = new StateField(nextID++, config.create, config.update, config.compare || ((a, b) => a === b), config);\n        if (config.provide)\n            field.provides = config.provide(field);\n        return field;\n    }\n    create(state) {\n        let init = state.facet(initField).find(i => i.field == this);\n        return ((init === null || init === void 0 ? void 0 : init.create) || this.createF)(state);\n    }\n    /**\n    @internal\n    */\n    slot(addresses) {\n        let idx = addresses[this.id] >> 1;\n        return {\n            create: (state) => {\n                state.values[idx] = this.create(state);\n                return 1 /* SlotStatus.Changed */;\n            },\n            update: (state, tr) => {\n                let oldVal = state.values[idx];\n                let value = this.updateF(oldVal, tr);\n                if (this.compareF(oldVal, value))\n                    return 0;\n                state.values[idx] = value;\n                return 1 /* SlotStatus.Changed */;\n            },\n            reconfigure: (state, oldState) => {\n                let init = state.facet(initField), oldInit = oldState.facet(initField), reInit;\n                if ((reInit = init.find(i => i.field == this)) && reInit != oldInit.find(i => i.field == this)) {\n                    state.values[idx] = reInit.create(state);\n                    return 1 /* SlotStatus.Changed */;\n                }\n                if (oldState.config.address[this.id] != null) {\n                    state.values[idx] = oldState.field(this);\n                    return 0;\n                }\n                state.values[idx] = this.create(state);\n                return 1 /* SlotStatus.Changed */;\n            }\n        };\n    }\n    /**\n    Returns an extension that enables this field and overrides the\n    way it is initialized. Can be useful when you need to provide a\n    non-default starting value for the field.\n    */\n    init(create) {\n        return [this, initField.of({ field: this, create })];\n    }\n    /**\n    State field instances can be used as\n    [`Extension`](https://codemirror.net/6/docs/ref/#state.Extension) values to enable the field in a\n    given state.\n    */\n    get extension() { return this; }\n}\nconst Prec_ = { lowest: 4, low: 3, default: 2, high: 1, highest: 0 };\nfunction prec(value) {\n    return (ext) => new PrecExtension(ext, value);\n}\n/**\nBy default extensions are registered in the order they are found\nin the flattened form of nested array that was provided.\nIndividual extension values can be assigned a precedence to\noverride this. Extensions that do not have a precedence set get\nthe precedence of the nearest parent with a precedence, or\n[`default`](https://codemirror.net/6/docs/ref/#state.Prec.default) if there is no such parent. The\nfinal ordering of extensions is determined by first sorting by\nprecedence and then by order within each precedence.\n*/\nconst Prec = {\n    /**\n    The highest precedence level, for extensions that should end up\n    near the start of the precedence ordering.\n    */\n    highest: /*@__PURE__*/prec(Prec_.highest),\n    /**\n    A higher-than-default precedence, for extensions that should\n    come before those with default precedence.\n    */\n    high: /*@__PURE__*/prec(Prec_.high),\n    /**\n    The default precedence, which is also used for extensions\n    without an explicit precedence.\n    */\n    default: /*@__PURE__*/prec(Prec_.default),\n    /**\n    A lower-than-default precedence.\n    */\n    low: /*@__PURE__*/prec(Prec_.low),\n    /**\n    The lowest precedence level. Meant for things that should end up\n    near the end of the extension order.\n    */\n    lowest: /*@__PURE__*/prec(Prec_.lowest)\n};\nclass PrecExtension {\n    constructor(inner, prec) {\n        this.inner = inner;\n        this.prec = prec;\n    }\n}\n/**\nExtension compartments can be used to make a configuration\ndynamic. By [wrapping](https://codemirror.net/6/docs/ref/#state.Compartment.of) part of your\nconfiguration in a compartment, you can later\n[replace](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure) that part through a\ntransaction.\n*/\nclass Compartment {\n    /**\n    Create an instance of this compartment to add to your [state\n    configuration](https://codemirror.net/6/docs/ref/#state.EditorStateConfig.extensions).\n    */\n    of(ext) { return new CompartmentInstance(this, ext); }\n    /**\n    Create an [effect](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) that\n    reconfigures this compartment.\n    */\n    reconfigure(content) {\n        return Compartment.reconfigure.of({ compartment: this, extension: content });\n    }\n    /**\n    Get the current content of the compartment in the state, or\n    `undefined` if it isn't present.\n    */\n    get(state) {\n        return state.config.compartments.get(this);\n    }\n}\nclass CompartmentInstance {\n    constructor(compartment, inner) {\n        this.compartment = compartment;\n        this.inner = inner;\n    }\n}\nclass Configuration {\n    constructor(base, compartments, dynamicSlots, address, staticValues, facets) {\n        this.base = base;\n        this.compartments = compartments;\n        this.dynamicSlots = dynamicSlots;\n        this.address = address;\n        this.staticValues = staticValues;\n        this.facets = facets;\n        this.statusTemplate = [];\n        while (this.statusTemplate.length < dynamicSlots.length)\n            this.statusTemplate.push(0 /* SlotStatus.Unresolved */);\n    }\n    staticFacet(facet) {\n        let addr = this.address[facet.id];\n        return addr == null ? facet.default : this.staticValues[addr >> 1];\n    }\n    static resolve(base, compartments, oldState) {\n        let fields = [];\n        let facets = Object.create(null);\n        let newCompartments = new Map();\n        for (let ext of flatten(base, compartments, newCompartments)) {\n            if (ext instanceof StateField)\n                fields.push(ext);\n            else\n                (facets[ext.facet.id] || (facets[ext.facet.id] = [])).push(ext);\n        }\n        let address = Object.create(null);\n        let staticValues = [];\n        let dynamicSlots = [];\n        for (let field of fields) {\n            address[field.id] = dynamicSlots.length << 1;\n            dynamicSlots.push(a => field.slot(a));\n        }\n        let oldFacets = oldState === null || oldState === void 0 ? void 0 : oldState.config.facets;\n        for (let id in facets) {\n            let providers = facets[id], facet = providers[0].facet;\n            let oldProviders = oldFacets && oldFacets[id] || [];\n            if (providers.every(p => p.type == 0 /* Provider.Static */)) {\n                address[facet.id] = (staticValues.length << 1) | 1;\n                if (sameArray(oldProviders, providers)) {\n                    staticValues.push(oldState.facet(facet));\n                }\n                else {\n                    let value = facet.combine(providers.map(p => p.value));\n                    staticValues.push(oldState && facet.compare(value, oldState.facet(facet)) ? oldState.facet(facet) : value);\n                }\n            }\n            else {\n                for (let p of providers) {\n                    if (p.type == 0 /* Provider.Static */) {\n                        address[p.id] = (staticValues.length << 1) | 1;\n                        staticValues.push(p.value);\n                    }\n                    else {\n                        address[p.id] = dynamicSlots.length << 1;\n                        dynamicSlots.push(a => p.dynamicSlot(a));\n                    }\n                }\n                address[facet.id] = dynamicSlots.length << 1;\n                dynamicSlots.push(a => dynamicFacetSlot(a, facet, providers));\n            }\n        }\n        let dynamic = dynamicSlots.map(f => f(address));\n        return new Configuration(base, newCompartments, dynamic, address, staticValues, facets);\n    }\n}\nfunction flatten(extension, compartments, newCompartments) {\n    let result = [[], [], [], [], []];\n    let seen = new Map();\n    function inner(ext, prec) {\n        let known = seen.get(ext);\n        if (known != null) {\n            if (known <= prec)\n                return;\n            let found = result[known].indexOf(ext);\n            if (found > -1)\n                result[known].splice(found, 1);\n            if (ext instanceof CompartmentInstance)\n                newCompartments.delete(ext.compartment);\n        }\n        seen.set(ext, prec);\n        if (Array.isArray(ext)) {\n            for (let e of ext)\n                inner(e, prec);\n        }\n        else if (ext instanceof CompartmentInstance) {\n            if (newCompartments.has(ext.compartment))\n                throw new RangeError(`Duplicate use of compartment in extensions`);\n            let content = compartments.get(ext.compartment) || ext.inner;\n            newCompartments.set(ext.compartment, content);\n            inner(content, prec);\n        }\n        else if (ext instanceof PrecExtension) {\n            inner(ext.inner, ext.prec);\n        }\n        else if (ext instanceof StateField) {\n            result[prec].push(ext);\n            if (ext.provides)\n                inner(ext.provides, prec);\n        }\n        else if (ext instanceof FacetProvider) {\n            result[prec].push(ext);\n            if (ext.facet.extensions)\n                inner(ext.facet.extensions, Prec_.default);\n        }\n        else {\n            let content = ext.extension;\n            if (!content)\n                throw new Error(`Unrecognized extension value in extension set (${ext}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);\n            inner(content, prec);\n        }\n    }\n    inner(extension, Prec_.default);\n    return result.reduce((a, b) => a.concat(b));\n}\nfunction ensureAddr(state, addr) {\n    if (addr & 1)\n        return 2 /* SlotStatus.Computed */;\n    let idx = addr >> 1;\n    let status = state.status[idx];\n    if (status == 4 /* SlotStatus.Computing */)\n        throw new Error(\"Cyclic dependency between fields and/or facets\");\n    if (status & 2 /* SlotStatus.Computed */)\n        return status;\n    state.status[idx] = 4 /* SlotStatus.Computing */;\n    let changed = state.computeSlot(state, state.config.dynamicSlots[idx]);\n    return state.status[idx] = 2 /* SlotStatus.Computed */ | changed;\n}\nfunction getAddr(state, addr) {\n    return addr & 1 ? state.config.staticValues[addr >> 1] : state.values[addr >> 1];\n}\n\nconst languageData = /*@__PURE__*/Facet.define();\nconst allowMultipleSelections = /*@__PURE__*/Facet.define({\n    combine: values => values.some(v => v),\n    static: true\n});\nconst lineSeparator = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : undefined,\n    static: true\n});\nconst changeFilter = /*@__PURE__*/Facet.define();\nconst transactionFilter = /*@__PURE__*/Facet.define();\nconst transactionExtender = /*@__PURE__*/Facet.define();\nconst readOnly = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : false\n});\n\n/**\nAnnotations are tagged values that are used to add metadata to\ntransactions in an extensible way. They should be used to model\nthings that effect the entire transaction (such as its [time\nstamp](https://codemirror.net/6/docs/ref/#state.Transaction^time) or information about its\n[origin](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent)). For effects that happen\n_alongside_ the other changes made by the transaction, [state\neffects](https://codemirror.net/6/docs/ref/#state.StateEffect) are more appropriate.\n*/\nclass Annotation {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The annotation type.\n    */\n    type, \n    /**\n    The value of this annotation.\n    */\n    value) {\n        this.type = type;\n        this.value = value;\n    }\n    /**\n    Define a new type of annotation.\n    */\n    static define() { return new AnnotationType(); }\n}\n/**\nMarker that identifies a type of [annotation](https://codemirror.net/6/docs/ref/#state.Annotation).\n*/\nclass AnnotationType {\n    /**\n    Create an instance of this annotation.\n    */\n    of(value) { return new Annotation(this, value); }\n}\n/**\nRepresentation of a type of state effect. Defined with\n[`StateEffect.define`](https://codemirror.net/6/docs/ref/#state.StateEffect^define).\n*/\nclass StateEffectType {\n    /**\n    @internal\n    */\n    constructor(\n    // The `any` types in these function types are there to work\n    // around TypeScript issue #37631, where the type guard on\n    // `StateEffect.is` mysteriously stops working when these properly\n    // have type `Value`.\n    /**\n    @internal\n    */\n    map) {\n        this.map = map;\n    }\n    /**\n    Create a [state effect](https://codemirror.net/6/docs/ref/#state.StateEffect) instance of this\n    type.\n    */\n    of(value) { return new StateEffect(this, value); }\n}\n/**\nState effects can be used to represent additional effects\nassociated with a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction.effects). They\nare often useful to model changes to custom [state\nfields](https://codemirror.net/6/docs/ref/#state.StateField), when those changes aren't implicit in\ndocument or selection changes.\n*/\nclass StateEffect {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    type, \n    /**\n    The value of this effect.\n    */\n    value) {\n        this.type = type;\n        this.value = value;\n    }\n    /**\n    Map this effect through a position mapping. Will return\n    `undefined` when that ends up deleting the effect.\n    */\n    map(mapping) {\n        let mapped = this.type.map(this.value, mapping);\n        return mapped === undefined ? undefined : mapped == this.value ? this : new StateEffect(this.type, mapped);\n    }\n    /**\n    Tells you whether this effect object is of a given\n    [type](https://codemirror.net/6/docs/ref/#state.StateEffectType).\n    */\n    is(type) { return this.type == type; }\n    /**\n    Define a new effect type. The type parameter indicates the type\n    of values that his effect holds. It should be a type that\n    doesn't include `undefined`, since that is used in\n    [mapping](https://codemirror.net/6/docs/ref/#state.StateEffect.map) to indicate that an effect is\n    removed.\n    */\n    static define(spec = {}) {\n        return new StateEffectType(spec.map || (v => v));\n    }\n    /**\n    Map an array of effects through a change set.\n    */\n    static mapEffects(effects, mapping) {\n        if (!effects.length)\n            return effects;\n        let result = [];\n        for (let effect of effects) {\n            let mapped = effect.map(mapping);\n            if (mapped)\n                result.push(mapped);\n        }\n        return result;\n    }\n}\n/**\nThis effect can be used to reconfigure the root extensions of\nthe editor. Doing this will discard any extensions\n[appended](https://codemirror.net/6/docs/ref/#state.StateEffect^appendConfig), but does not reset\nthe content of [reconfigured](https://codemirror.net/6/docs/ref/#state.Compartment.reconfigure)\ncompartments.\n*/\nStateEffect.reconfigure = /*@__PURE__*/StateEffect.define();\n/**\nAppend extensions to the top-level configuration of the editor.\n*/\nStateEffect.appendConfig = /*@__PURE__*/StateEffect.define();\n/**\nChanges to the editor state are grouped into transactions.\nTypically, a user action creates a single transaction, which may\ncontain any number of document changes, may change the selection,\nor have other effects. Create a transaction by calling\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update), or immediately\ndispatch one by calling\n[`EditorView.dispatch`](https://codemirror.net/6/docs/ref/#view.EditorView.dispatch).\n*/\nclass Transaction {\n    constructor(\n    /**\n    The state from which the transaction starts.\n    */\n    startState, \n    /**\n    The document changes made by this transaction.\n    */\n    changes, \n    /**\n    The selection set by this transaction, or undefined if it\n    doesn't explicitly set a selection.\n    */\n    selection, \n    /**\n    The effects added to the transaction.\n    */\n    effects, \n    /**\n    @internal\n    */\n    annotations, \n    /**\n    Whether the selection should be scrolled into view after this\n    transaction is dispatched.\n    */\n    scrollIntoView) {\n        this.startState = startState;\n        this.changes = changes;\n        this.selection = selection;\n        this.effects = effects;\n        this.annotations = annotations;\n        this.scrollIntoView = scrollIntoView;\n        /**\n        @internal\n        */\n        this._doc = null;\n        /**\n        @internal\n        */\n        this._state = null;\n        if (selection)\n            checkSelection(selection, changes.newLength);\n        if (!annotations.some((a) => a.type == Transaction.time))\n            this.annotations = annotations.concat(Transaction.time.of(Date.now()));\n    }\n    /**\n    @internal\n    */\n    static create(startState, changes, selection, effects, annotations, scrollIntoView) {\n        return new Transaction(startState, changes, selection, effects, annotations, scrollIntoView);\n    }\n    /**\n    The new document produced by the transaction. Contrary to\n    [`.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state)`.doc`, accessing this won't\n    force the entire new state to be computed right away, so it is\n    recommended that [transaction\n    filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) use this getter\n    when they need to look at the new document.\n    */\n    get newDoc() {\n        return this._doc || (this._doc = this.changes.apply(this.startState.doc));\n    }\n    /**\n    The new selection produced by the transaction. If\n    [`this.selection`](https://codemirror.net/6/docs/ref/#state.Transaction.selection) is undefined,\n    this will [map](https://codemirror.net/6/docs/ref/#state.EditorSelection.map) the start state's\n    current selection through the changes made by the transaction.\n    */\n    get newSelection() {\n        return this.selection || this.startState.selection.map(this.changes);\n    }\n    /**\n    The new state created by the transaction. Computed on demand\n    (but retained for subsequent access), so it is recommended not to\n    access it in [transaction\n    filters](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter) when possible.\n    */\n    get state() {\n        if (!this._state)\n            this.startState.applyTransaction(this);\n        return this._state;\n    }\n    /**\n    Get the value of the given annotation type, if any.\n    */\n    annotation(type) {\n        for (let ann of this.annotations)\n            if (ann.type == type)\n                return ann.value;\n        return undefined;\n    }\n    /**\n    Indicates whether the transaction changed the document.\n    */\n    get docChanged() { return !this.changes.empty; }\n    /**\n    Indicates whether this transaction reconfigures the state\n    (through a [configuration compartment](https://codemirror.net/6/docs/ref/#state.Compartment) or\n    with a top-level configuration\n    [effect](https://codemirror.net/6/docs/ref/#state.StateEffect^reconfigure).\n    */\n    get reconfigured() { return this.startState.config != this.state.config; }\n    /**\n    Returns true if the transaction has a [user\n    event](https://codemirror.net/6/docs/ref/#state.Transaction^userEvent) annotation that is equal to\n    or more specific than `event`. For example, if the transaction\n    has `\"select.pointer\"` as user event, `\"select\"` and\n    `\"select.pointer\"` will match it.\n    */\n    isUserEvent(event) {\n        let e = this.annotation(Transaction.userEvent);\n        return !!(e && (e == event || e.length > event.length && e.slice(0, event.length) == event && e[event.length] == \".\"));\n    }\n}\n/**\nAnnotation used to store transaction timestamps. Automatically\nadded to every transaction, holding `Date.now()`.\n*/\nTransaction.time = /*@__PURE__*/Annotation.define();\n/**\nAnnotation used to associate a transaction with a user interface\nevent. Holds a string identifying the event, using a\ndot-separated format to support attaching more specific\ninformation. The events used by the core libraries are:\n\n - `\"input\"` when content is entered\n   - `\"input.type\"` for typed input\n     - `\"input.type.compose\"` for composition\n   - `\"input.paste\"` for pasted input\n   - `\"input.drop\"` when adding content with drag-and-drop\n   - `\"input.complete\"` when autocompleting\n - `\"delete\"` when the user deletes content\n   - `\"delete.selection\"` when deleting the selection\n   - `\"delete.forward\"` when deleting forward from the selection\n   - `\"delete.backward\"` when deleting backward from the selection\n   - `\"delete.cut\"` when cutting to the clipboard\n - `\"move\"` when content is moved\n   - `\"move.drop\"` when content is moved within the editor through drag-and-drop\n - `\"select\"` when explicitly changing the selection\n   - `\"select.pointer\"` when selecting with a mouse or other pointing device\n - `\"undo\"` and `\"redo\"` for history actions\n\nUse [`isUserEvent`](https://codemirror.net/6/docs/ref/#state.Transaction.isUserEvent) to check\nwhether the annotation matches a given event.\n*/\nTransaction.userEvent = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating whether a transaction should be added to\nthe undo history or not.\n*/\nTransaction.addToHistory = /*@__PURE__*/Annotation.define();\n/**\nAnnotation indicating (when present and true) that a transaction\nrepresents a change made by some other actor, not the user. This\nis used, for example, to tag other people's changes in\ncollaborative editing.\n*/\nTransaction.remote = /*@__PURE__*/Annotation.define();\nfunction joinRanges(a, b) {\n    let result = [];\n    for (let iA = 0, iB = 0;;) {\n        let from, to;\n        if (iA < a.length && (iB == b.length || b[iB] >= a[iA])) {\n            from = a[iA++];\n            to = a[iA++];\n        }\n        else if (iB < b.length) {\n            from = b[iB++];\n            to = b[iB++];\n        }\n        else\n            return result;\n        if (!result.length || result[result.length - 1] < from)\n            result.push(from, to);\n        else if (result[result.length - 1] < to)\n            result[result.length - 1] = to;\n    }\n}\nfunction mergeTransaction(a, b, sequential) {\n    var _a;\n    let mapForA, mapForB, changes;\n    if (sequential) {\n        mapForA = b.changes;\n        mapForB = ChangeSet.empty(b.changes.length);\n        changes = a.changes.compose(b.changes);\n    }\n    else {\n        mapForA = b.changes.map(a.changes);\n        mapForB = a.changes.mapDesc(b.changes, true);\n        changes = a.changes.compose(mapForA);\n    }\n    return {\n        changes,\n        selection: b.selection ? b.selection.map(mapForB) : (_a = a.selection) === null || _a === void 0 ? void 0 : _a.map(mapForA),\n        effects: StateEffect.mapEffects(a.effects, mapForA).concat(StateEffect.mapEffects(b.effects, mapForB)),\n        annotations: a.annotations.length ? a.annotations.concat(b.annotations) : b.annotations,\n        scrollIntoView: a.scrollIntoView || b.scrollIntoView\n    };\n}\nfunction resolveTransactionInner(state, spec, docSize) {\n    let sel = spec.selection, annotations = asArray(spec.annotations);\n    if (spec.userEvent)\n        annotations = annotations.concat(Transaction.userEvent.of(spec.userEvent));\n    return {\n        changes: spec.changes instanceof ChangeSet ? spec.changes\n            : ChangeSet.of(spec.changes || [], docSize, state.facet(lineSeparator)),\n        selection: sel && (sel instanceof EditorSelection ? sel : EditorSelection.single(sel.anchor, sel.head)),\n        effects: asArray(spec.effects),\n        annotations,\n        scrollIntoView: !!spec.scrollIntoView\n    };\n}\nfunction resolveTransaction(state, specs, filter) {\n    let s = resolveTransactionInner(state, specs.length ? specs[0] : {}, state.doc.length);\n    if (specs.length && specs[0].filter === false)\n        filter = false;\n    for (let i = 1; i < specs.length; i++) {\n        if (specs[i].filter === false)\n            filter = false;\n        let seq = !!specs[i].sequential;\n        s = mergeTransaction(s, resolveTransactionInner(state, specs[i], seq ? s.changes.newLength : state.doc.length), seq);\n    }\n    let tr = Transaction.create(state, s.changes, s.selection, s.effects, s.annotations, s.scrollIntoView);\n    return extendTransaction(filter ? filterTransaction(tr) : tr);\n}\n// Finish a transaction by applying filters if necessary.\nfunction filterTransaction(tr) {\n    let state = tr.startState;\n    // Change filters\n    let result = true;\n    for (let filter of state.facet(changeFilter)) {\n        let value = filter(tr);\n        if (value === false) {\n            result = false;\n            break;\n        }\n        if (Array.isArray(value))\n            result = result === true ? value : joinRanges(result, value);\n    }\n    if (result !== true) {\n        let changes, back;\n        if (result === false) {\n            back = tr.changes.invertedDesc;\n            changes = ChangeSet.empty(state.doc.length);\n        }\n        else {\n            let filtered = tr.changes.filter(result);\n            changes = filtered.changes;\n            back = filtered.filtered.mapDesc(filtered.changes).invertedDesc;\n        }\n        tr = Transaction.create(state, changes, tr.selection && tr.selection.map(back), StateEffect.mapEffects(tr.effects, back), tr.annotations, tr.scrollIntoView);\n    }\n    // Transaction filters\n    let filters = state.facet(transactionFilter);\n    for (let i = filters.length - 1; i >= 0; i--) {\n        let filtered = filters[i](tr);\n        if (filtered instanceof Transaction)\n            tr = filtered;\n        else if (Array.isArray(filtered) && filtered.length == 1 && filtered[0] instanceof Transaction)\n            tr = filtered[0];\n        else\n            tr = resolveTransaction(state, asArray(filtered), false);\n    }\n    return tr;\n}\nfunction extendTransaction(tr) {\n    let state = tr.startState, extenders = state.facet(transactionExtender), spec = tr;\n    for (let i = extenders.length - 1; i >= 0; i--) {\n        let extension = extenders[i](tr);\n        if (extension && Object.keys(extension).length)\n            spec = mergeTransaction(spec, resolveTransactionInner(state, extension, tr.changes.newLength), true);\n    }\n    return spec == tr ? tr : Transaction.create(state, tr.changes, tr.selection, spec.effects, spec.annotations, spec.scrollIntoView);\n}\nconst none = [];\nfunction asArray(value) {\n    return value == null ? none : Array.isArray(value) ? value : [value];\n}\n\n/**\nThe categories produced by a [character\ncategorizer](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer). These are used\ndo things like selecting by word.\n*/\nvar CharCategory = /*@__PURE__*/(function (CharCategory) {\n    /**\n    Word characters.\n    */\n    CharCategory[CharCategory[\"Word\"] = 0] = \"Word\";\n    /**\n    Whitespace.\n    */\n    CharCategory[CharCategory[\"Space\"] = 1] = \"Space\";\n    /**\n    Anything else.\n    */\n    CharCategory[CharCategory[\"Other\"] = 2] = \"Other\";\nreturn CharCategory})(CharCategory || (CharCategory = {}));\nconst nonASCIISingleCaseWordChar = /[\\u00df\\u0587\\u0590-\\u05f4\\u0600-\\u06ff\\u3040-\\u309f\\u30a0-\\u30ff\\u3400-\\u4db5\\u4e00-\\u9fcc\\uac00-\\ud7af]/;\nlet wordChar;\ntry {\n    wordChar = /*@__PURE__*/new RegExp(\"[\\\\p{Alphabetic}\\\\p{Number}_]\", \"u\");\n}\ncatch (_) { }\nfunction hasWordChar(str) {\n    if (wordChar)\n        return wordChar.test(str);\n    for (let i = 0; i < str.length; i++) {\n        let ch = str[i];\n        if (/\\w/.test(ch) || ch > \"\\x80\" && (ch.toUpperCase() != ch.toLowerCase() || nonASCIISingleCaseWordChar.test(ch)))\n            return true;\n    }\n    return false;\n}\nfunction makeCategorizer(wordChars) {\n    return (char) => {\n        if (!/\\S/.test(char))\n            return CharCategory.Space;\n        if (hasWordChar(char))\n            return CharCategory.Word;\n        for (let i = 0; i < wordChars.length; i++)\n            if (char.indexOf(wordChars[i]) > -1)\n                return CharCategory.Word;\n        return CharCategory.Other;\n    };\n}\n\n/**\nThe editor state class is a persistent (immutable) data structure.\nTo update a state, you [create](https://codemirror.net/6/docs/ref/#state.EditorState.update) a\n[transaction](https://codemirror.net/6/docs/ref/#state.Transaction), which produces a _new_ state\ninstance, without modifying the original object.\n\nAs such, _never_ mutate properties of a state directly. That'll\njust break things.\n*/\nclass EditorState {\n    constructor(\n    /**\n    @internal\n    */\n    config, \n    /**\n    The current document.\n    */\n    doc, \n    /**\n    The current selection.\n    */\n    selection, \n    /**\n    @internal\n    */\n    values, computeSlot, tr) {\n        this.config = config;\n        this.doc = doc;\n        this.selection = selection;\n        this.values = values;\n        this.status = config.statusTemplate.slice();\n        this.computeSlot = computeSlot;\n        // Fill in the computed state immediately, so that further queries\n        // for it made during the update return this state\n        if (tr)\n            tr._state = this;\n        for (let i = 0; i < this.config.dynamicSlots.length; i++)\n            ensureAddr(this, i << 1);\n        this.computeSlot = null;\n    }\n    field(field, require = true) {\n        let addr = this.config.address[field.id];\n        if (addr == null) {\n            if (require)\n                throw new RangeError(\"Field is not present in this state\");\n            return undefined;\n        }\n        ensureAddr(this, addr);\n        return getAddr(this, addr);\n    }\n    /**\n    Create a [transaction](https://codemirror.net/6/docs/ref/#state.Transaction) that updates this\n    state. Any number of [transaction specs](https://codemirror.net/6/docs/ref/#state.TransactionSpec)\n    can be passed. Unless\n    [`sequential`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.sequential) is set, the\n    [changes](https://codemirror.net/6/docs/ref/#state.TransactionSpec.changes) (if any) of each spec\n    are assumed to start in the _current_ document (not the document\n    produced by previous specs), and its\n    [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection) and\n    [effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects) are assumed to refer\n    to the document created by its _own_ changes. The resulting\n    transaction contains the combined effect of all the different\n    specs. For [selection](https://codemirror.net/6/docs/ref/#state.TransactionSpec.selection), later\n    specs take precedence over earlier ones.\n    */\n    update(...specs) {\n        return resolveTransaction(this, specs, true);\n    }\n    /**\n    @internal\n    */\n    applyTransaction(tr) {\n        let conf = this.config, { base, compartments } = conf;\n        for (let effect of tr.effects) {\n            if (effect.is(Compartment.reconfigure)) {\n                if (conf) {\n                    compartments = new Map;\n                    conf.compartments.forEach((val, key) => compartments.set(key, val));\n                    conf = null;\n                }\n                compartments.set(effect.value.compartment, effect.value.extension);\n            }\n            else if (effect.is(StateEffect.reconfigure)) {\n                conf = null;\n                base = effect.value;\n            }\n            else if (effect.is(StateEffect.appendConfig)) {\n                conf = null;\n                base = asArray(base).concat(effect.value);\n            }\n        }\n        let startValues;\n        if (!conf) {\n            conf = Configuration.resolve(base, compartments, this);\n            let intermediateState = new EditorState(conf, this.doc, this.selection, conf.dynamicSlots.map(() => null), (state, slot) => slot.reconfigure(state, this), null);\n            startValues = intermediateState.values;\n        }\n        else {\n            startValues = tr.startState.values.slice();\n        }\n        let selection = tr.startState.facet(allowMultipleSelections) ? tr.newSelection : tr.newSelection.asSingle();\n        new EditorState(conf, tr.newDoc, selection, startValues, (state, slot) => slot.update(state, tr), tr);\n    }\n    /**\n    Create a [transaction spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec) that\n    replaces every selection range with the given content.\n    */\n    replaceSelection(text) {\n        if (typeof text == \"string\")\n            text = this.toText(text);\n        return this.changeByRange(range => ({ changes: { from: range.from, to: range.to, insert: text },\n            range: EditorSelection.cursor(range.from + text.length) }));\n    }\n    /**\n    Create a set of changes and a new selection by running the given\n    function for each range in the active selection. The function\n    can return an optional set of changes (in the coordinate space\n    of the start document), plus an updated range (in the coordinate\n    space of the document produced by the call's own changes). This\n    method will merge all the changes and ranges into a single\n    changeset and selection, and return it as a [transaction\n    spec](https://codemirror.net/6/docs/ref/#state.TransactionSpec), which can be passed to\n    [`update`](https://codemirror.net/6/docs/ref/#state.EditorState.update).\n    */\n    changeByRange(f) {\n        let sel = this.selection;\n        let result1 = f(sel.ranges[0]);\n        let changes = this.changes(result1.changes), ranges = [result1.range];\n        let effects = asArray(result1.effects);\n        for (let i = 1; i < sel.ranges.length; i++) {\n            let result = f(sel.ranges[i]);\n            let newChanges = this.changes(result.changes), newMapped = newChanges.map(changes);\n            for (let j = 0; j < i; j++)\n                ranges[j] = ranges[j].map(newMapped);\n            let mapBy = changes.mapDesc(newChanges, true);\n            ranges.push(result.range.map(mapBy));\n            changes = changes.compose(newMapped);\n            effects = StateEffect.mapEffects(effects, newMapped).concat(StateEffect.mapEffects(asArray(result.effects), mapBy));\n        }\n        return {\n            changes,\n            selection: EditorSelection.create(ranges, sel.mainIndex),\n            effects\n        };\n    }\n    /**\n    Create a [change set](https://codemirror.net/6/docs/ref/#state.ChangeSet) from the given change\n    description, taking the state's document length and line\n    separator into account.\n    */\n    changes(spec = []) {\n        if (spec instanceof ChangeSet)\n            return spec;\n        return ChangeSet.of(spec, this.doc.length, this.facet(EditorState.lineSeparator));\n    }\n    /**\n    Using the state's [line\n    separator](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator), create a\n    [`Text`](https://codemirror.net/6/docs/ref/#state.Text) instance from the given string.\n    */\n    toText(string) {\n        return Text.of(string.split(this.facet(EditorState.lineSeparator) || DefaultSplit));\n    }\n    /**\n    Return the given range of the document as a string.\n    */\n    sliceDoc(from = 0, to = this.doc.length) {\n        return this.doc.sliceString(from, to, this.lineBreak);\n    }\n    /**\n    Get the value of a state [facet](https://codemirror.net/6/docs/ref/#state.Facet).\n    */\n    facet(facet) {\n        let addr = this.config.address[facet.id];\n        if (addr == null)\n            return facet.default;\n        ensureAddr(this, addr);\n        return getAddr(this, addr);\n    }\n    /**\n    Convert this state to a JSON-serializable object. When custom\n    fields should be serialized, you can pass them in as an object\n    mapping property names (in the resulting object, which should\n    not use `doc` or `selection`) to fields.\n    */\n    toJSON(fields) {\n        let result = {\n            doc: this.sliceDoc(),\n            selection: this.selection.toJSON()\n        };\n        if (fields)\n            for (let prop in fields) {\n                let value = fields[prop];\n                if (value instanceof StateField && this.config.address[value.id] != null)\n                    result[prop] = value.spec.toJSON(this.field(fields[prop]), this);\n            }\n        return result;\n    }\n    /**\n    Deserialize a state from its JSON representation. When custom\n    fields should be deserialized, pass the same object you passed\n    to [`toJSON`](https://codemirror.net/6/docs/ref/#state.EditorState.toJSON) when serializing as\n    third argument.\n    */\n    static fromJSON(json, config = {}, fields) {\n        if (!json || typeof json.doc != \"string\")\n            throw new RangeError(\"Invalid JSON representation for EditorState\");\n        let fieldInit = [];\n        if (fields)\n            for (let prop in fields) {\n                if (Object.prototype.hasOwnProperty.call(json, prop)) {\n                    let field = fields[prop], value = json[prop];\n                    fieldInit.push(field.init(state => field.spec.fromJSON(value, state)));\n                }\n            }\n        return EditorState.create({\n            doc: json.doc,\n            selection: EditorSelection.fromJSON(json.selection),\n            extensions: config.extensions ? fieldInit.concat([config.extensions]) : fieldInit\n        });\n    }\n    /**\n    Create a new state. You'll usually only need this when\n    initializing an editor—updated states are created by applying\n    transactions.\n    */\n    static create(config = {}) {\n        let configuration = Configuration.resolve(config.extensions || [], new Map);\n        let doc = config.doc instanceof Text ? config.doc\n            : Text.of((config.doc || \"\").split(configuration.staticFacet(EditorState.lineSeparator) || DefaultSplit));\n        let selection = !config.selection ? EditorSelection.single(0)\n            : config.selection instanceof EditorSelection ? config.selection\n                : EditorSelection.single(config.selection.anchor, config.selection.head);\n        checkSelection(selection, doc.length);\n        if (!configuration.staticFacet(allowMultipleSelections))\n            selection = selection.asSingle();\n        return new EditorState(configuration, doc, selection, configuration.dynamicSlots.map(() => null), (state, slot) => slot.create(state), null);\n    }\n    /**\n    The size (in columns) of a tab in the document, determined by\n    the [`tabSize`](https://codemirror.net/6/docs/ref/#state.EditorState^tabSize) facet.\n    */\n    get tabSize() { return this.facet(EditorState.tabSize); }\n    /**\n    Get the proper [line-break](https://codemirror.net/6/docs/ref/#state.EditorState^lineSeparator)\n    string for this state.\n    */\n    get lineBreak() { return this.facet(EditorState.lineSeparator) || \"\\n\"; }\n    /**\n    Returns true when the editor is\n    [configured](https://codemirror.net/6/docs/ref/#state.EditorState^readOnly) to be read-only.\n    */\n    get readOnly() { return this.facet(readOnly); }\n    /**\n    Look up a translation for the given phrase (via the\n    [`phrases`](https://codemirror.net/6/docs/ref/#state.EditorState^phrases) facet), or return the\n    original string if no translation is found.\n    \n    If additional arguments are passed, they will be inserted in\n    place of markers like `$1` (for the first value) and `$2`, etc.\n    A single `$` is equivalent to `$1`, and `$$` will produce a\n    literal dollar sign.\n    */\n    phrase(phrase, ...insert) {\n        for (let map of this.facet(EditorState.phrases))\n            if (Object.prototype.hasOwnProperty.call(map, phrase)) {\n                phrase = map[phrase];\n                break;\n            }\n        if (insert.length)\n            phrase = phrase.replace(/\\$(\\$|\\d*)/g, (m, i) => {\n                if (i == \"$\")\n                    return \"$\";\n                let n = +(i || 1);\n                return !n || n > insert.length ? m : insert[n - 1];\n            });\n        return phrase;\n    }\n    /**\n    Find the values for a given language data field, provided by the\n    the [`languageData`](https://codemirror.net/6/docs/ref/#state.EditorState^languageData) facet.\n    \n    Examples of language data fields are...\n    \n    - [`\"commentTokens\"`](https://codemirror.net/6/docs/ref/#commands.CommentTokens) for specifying\n      comment syntax.\n    - [`\"autocomplete\"`](https://codemirror.net/6/docs/ref/#autocomplete.autocompletion^config.override)\n      for providing language-specific completion sources.\n    - [`\"wordChars\"`](https://codemirror.net/6/docs/ref/#state.EditorState.charCategorizer) for adding\n      characters that should be considered part of words in this\n      language.\n    - [`\"closeBrackets\"`](https://codemirror.net/6/docs/ref/#autocomplete.CloseBracketConfig) controls\n      bracket closing behavior.\n    */\n    languageDataAt(name, pos, side = -1) {\n        let values = [];\n        for (let provider of this.facet(languageData)) {\n            for (let result of provider(this, pos, side)) {\n                if (Object.prototype.hasOwnProperty.call(result, name))\n                    values.push(result[name]);\n            }\n        }\n        return values;\n    }\n    /**\n    Return a function that can categorize strings (expected to\n    represent a single [grapheme cluster](https://codemirror.net/6/docs/ref/#state.findClusterBreak))\n    into one of:\n    \n     - Word (contains an alphanumeric character or a character\n       explicitly listed in the local language's `\"wordChars\"`\n       language data, which should be a string)\n     - Space (contains only whitespace)\n     - Other (anything else)\n    */\n    charCategorizer(at) {\n        return makeCategorizer(this.languageDataAt(\"wordChars\", at).join(\"\"));\n    }\n    /**\n    Find the word at the given position, meaning the range\n    containing all [word](https://codemirror.net/6/docs/ref/#state.CharCategory.Word) characters\n    around it. If no word characters are adjacent to the position,\n    this returns null.\n    */\n    wordAt(pos) {\n        let { text, from, length } = this.doc.lineAt(pos);\n        let cat = this.charCategorizer(pos);\n        let start = pos - from, end = pos - from;\n        while (start > 0) {\n            let prev = findClusterBreak(text, start, false);\n            if (cat(text.slice(prev, start)) != CharCategory.Word)\n                break;\n            start = prev;\n        }\n        while (end < length) {\n            let next = findClusterBreak(text, end);\n            if (cat(text.slice(end, next)) != CharCategory.Word)\n                break;\n            end = next;\n        }\n        return start == end ? null : EditorSelection.range(start + from, end + from);\n    }\n}\n/**\nA facet that, when enabled, causes the editor to allow multiple\nranges to be selected. Be careful though, because by default the\neditor relies on the native DOM selection, which cannot handle\nmultiple selections. An extension like\n[`drawSelection`](https://codemirror.net/6/docs/ref/#view.drawSelection) can be used to make\nsecondary selections visible to the user.\n*/\nEditorState.allowMultipleSelections = allowMultipleSelections;\n/**\nConfigures the tab size to use in this state. The first\n(highest-precedence) value of the facet is used. If no value is\ngiven, this defaults to 4.\n*/\nEditorState.tabSize = /*@__PURE__*/Facet.define({\n    combine: values => values.length ? values[0] : 4\n});\n/**\nThe line separator to use. By default, any of `\"\\n\"`, `\"\\r\\n\"`\nand `\"\\r\"` is treated as a separator when splitting lines, and\nlines are joined with `\"\\n\"`.\n\nWhen you configure a value here, only that precise separator\nwill be used, allowing you to round-trip documents through the\neditor without normalizing line separators.\n*/\nEditorState.lineSeparator = lineSeparator;\n/**\nThis facet controls the value of the\n[`readOnly`](https://codemirror.net/6/docs/ref/#state.EditorState.readOnly) getter, which is\nconsulted by commands and extensions that implement editing\nfunctionality to determine whether they should apply. It\ndefaults to false, but when its highest-precedence value is\n`true`, such functionality disables itself.\n\nNot to be confused with\n[`EditorView.editable`](https://codemirror.net/6/docs/ref/#view.EditorView^editable), which\ncontrols whether the editor's DOM is set to be editable (and\nthus focusable).\n*/\nEditorState.readOnly = readOnly;\n/**\nRegisters translation phrases. The\n[`phrase`](https://codemirror.net/6/docs/ref/#state.EditorState.phrase) method will look through\nall objects registered with this facet to find translations for\nits argument.\n*/\nEditorState.phrases = /*@__PURE__*/Facet.define({\n    compare(a, b) {\n        let kA = Object.keys(a), kB = Object.keys(b);\n        return kA.length == kB.length && kA.every(k => a[k] == b[k]);\n    }\n});\n/**\nA facet used to register [language\ndata](https://codemirror.net/6/docs/ref/#state.EditorState.languageDataAt) providers.\n*/\nEditorState.languageData = languageData;\n/**\nFacet used to register change filters, which are called for each\ntransaction (unless explicitly\n[disabled](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter)), and can suppress\npart of the transaction's changes.\n\nSuch a function can return `true` to indicate that it doesn't\nwant to do anything, `false` to completely stop the changes in\nthe transaction, or a set of ranges in which changes should be\nsuppressed. Such ranges are represented as an array of numbers,\nwith each pair of two numbers indicating the start and end of a\nrange. So for example `[10, 20, 100, 110]` suppresses changes\nbetween 10 and 20, and between 100 and 110.\n*/\nEditorState.changeFilter = changeFilter;\n/**\nFacet used to register a hook that gets a chance to update or\nreplace transaction specs before they are applied. This will\nonly be applied for transactions that don't have\n[`filter`](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter) set to `false`. You\ncan either return a single transaction spec (possibly the input\ntransaction), or an array of specs (which will be combined in\nthe same way as the arguments to\n[`EditorState.update`](https://codemirror.net/6/docs/ref/#state.EditorState.update)).\n\nWhen possible, it is recommended to avoid accessing\n[`Transaction.state`](https://codemirror.net/6/docs/ref/#state.Transaction.state) in a filter,\nsince it will force creation of a state that will then be\ndiscarded again, if the transaction is actually filtered.\n\n(This functionality should be used with care. Indiscriminately\nmodifying transaction is likely to break something or degrade\nthe user experience.)\n*/\nEditorState.transactionFilter = transactionFilter;\n/**\nThis is a more limited form of\n[`transactionFilter`](https://codemirror.net/6/docs/ref/#state.EditorState^transactionFilter),\nwhich can only add\n[annotations](https://codemirror.net/6/docs/ref/#state.TransactionSpec.annotations) and\n[effects](https://codemirror.net/6/docs/ref/#state.TransactionSpec.effects). _But_, this type\nof filter runs even if the transaction has disabled regular\n[filtering](https://codemirror.net/6/docs/ref/#state.TransactionSpec.filter), making it suitable\nfor effects that don't need to touch the changes or selection,\nbut do want to process every transaction.\n\nExtenders run _after_ filters, when both are present.\n*/\nEditorState.transactionExtender = transactionExtender;\nCompartment.reconfigure = /*@__PURE__*/StateEffect.define();\n\n/**\nUtility function for combining behaviors to fill in a config\nobject from an array of provided configs. `defaults` should hold\ndefault values for all optional fields in `Config`.\n\nThe function will, by default, error\nwhen a field gets two values that aren't `===`-equal, but you can\nprovide combine functions per field to do something else.\n*/\nfunction combineConfig(configs, defaults, // Should hold only the optional properties of Config, but I haven't managed to express that\ncombine = {}) {\n    let result = {};\n    for (let config of configs)\n        for (let key of Object.keys(config)) {\n            let value = config[key], current = result[key];\n            if (current === undefined)\n                result[key] = value;\n            else if (current === value || value === undefined) ; // No conflict\n            else if (Object.hasOwnProperty.call(combine, key))\n                result[key] = combine[key](current, value);\n            else\n                throw new Error(\"Config merge conflict for field \" + key);\n        }\n    for (let key in defaults)\n        if (result[key] === undefined)\n            result[key] = defaults[key];\n    return result;\n}\n\n/**\nEach range is associated with a value, which must inherit from\nthis class.\n*/\nclass RangeValue {\n    /**\n    Compare this value with another value. Used when comparing\n    rangesets. The default implementation compares by identity.\n    Unless you are only creating a fixed number of unique instances\n    of your value type, it is a good idea to implement this\n    properly.\n    */\n    eq(other) { return this == other; }\n    /**\n    Create a [range](https://codemirror.net/6/docs/ref/#state.Range) with this value.\n    */\n    range(from, to = from) { return Range.create(from, to, this); }\n}\nRangeValue.prototype.startSide = RangeValue.prototype.endSide = 0;\nRangeValue.prototype.point = false;\nRangeValue.prototype.mapMode = MapMode.TrackDel;\n/**\nA range associates a value with a range of positions.\n*/\nclass Range {\n    constructor(\n    /**\n    The range's start position.\n    */\n    from, \n    /**\n    Its end position.\n    */\n    to, \n    /**\n    The value associated with this range.\n    */\n    value) {\n        this.from = from;\n        this.to = to;\n        this.value = value;\n    }\n    /**\n    @internal\n    */\n    static create(from, to, value) {\n        return new Range(from, to, value);\n    }\n}\nfunction cmpRange(a, b) {\n    return a.from - b.from || a.value.startSide - b.value.startSide;\n}\nclass Chunk {\n    constructor(from, to, value, \n    // Chunks are marked with the largest point that occurs\n    // in them (or -1 for no points), so that scans that are\n    // only interested in points (such as the\n    // heightmap-related logic) can skip range-only chunks.\n    maxPoint) {\n        this.from = from;\n        this.to = to;\n        this.value = value;\n        this.maxPoint = maxPoint;\n    }\n    get length() { return this.to[this.to.length - 1]; }\n    // Find the index of the given position and side. Use the ranges'\n    // `from` pos when `end == false`, `to` when `end == true`.\n    findIndex(pos, side, end, startAt = 0) {\n        let arr = end ? this.to : this.from;\n        for (let lo = startAt, hi = arr.length;;) {\n            if (lo == hi)\n                return lo;\n            let mid = (lo + hi) >> 1;\n            let diff = arr[mid] - pos || (end ? this.value[mid].endSide : this.value[mid].startSide) - side;\n            if (mid == lo)\n                return diff >= 0 ? lo : hi;\n            if (diff >= 0)\n                hi = mid;\n            else\n                lo = mid + 1;\n        }\n    }\n    between(offset, from, to, f) {\n        for (let i = this.findIndex(from, -********** /* C.Far */, true), e = this.findIndex(to, ********** /* C.Far */, false, i); i < e; i++)\n            if (f(this.from[i] + offset, this.to[i] + offset, this.value[i]) === false)\n                return false;\n    }\n    map(offset, changes) {\n        let value = [], from = [], to = [], newPos = -1, maxPoint = -1;\n        for (let i = 0; i < this.value.length; i++) {\n            let val = this.value[i], curFrom = this.from[i] + offset, curTo = this.to[i] + offset, newFrom, newTo;\n            if (curFrom == curTo) {\n                let mapped = changes.mapPos(curFrom, val.startSide, val.mapMode);\n                if (mapped == null)\n                    continue;\n                newFrom = newTo = mapped;\n                if (val.startSide != val.endSide) {\n                    newTo = changes.mapPos(curFrom, val.endSide);\n                    if (newTo < newFrom)\n                        continue;\n                }\n            }\n            else {\n                newFrom = changes.mapPos(curFrom, val.startSide);\n                newTo = changes.mapPos(curTo, val.endSide);\n                if (newFrom > newTo || newFrom == newTo && val.startSide > 0 && val.endSide <= 0)\n                    continue;\n            }\n            if ((newTo - newFrom || val.endSide - val.startSide) < 0)\n                continue;\n            if (newPos < 0)\n                newPos = newFrom;\n            if (val.point)\n                maxPoint = Math.max(maxPoint, newTo - newFrom);\n            value.push(val);\n            from.push(newFrom - newPos);\n            to.push(newTo - newPos);\n        }\n        return { mapped: value.length ? new Chunk(from, to, value, maxPoint) : null, pos: newPos };\n    }\n}\n/**\nA range set stores a collection of [ranges](https://codemirror.net/6/docs/ref/#state.Range) in a\nway that makes them efficient to [map](https://codemirror.net/6/docs/ref/#state.RangeSet.map) and\n[update](https://codemirror.net/6/docs/ref/#state.RangeSet.update). This is an immutable data\nstructure.\n*/\nclass RangeSet {\n    constructor(\n    /**\n    @internal\n    */\n    chunkPos, \n    /**\n    @internal\n    */\n    chunk, \n    /**\n    @internal\n    */\n    nextLayer, \n    /**\n    @internal\n    */\n    maxPoint) {\n        this.chunkPos = chunkPos;\n        this.chunk = chunk;\n        this.nextLayer = nextLayer;\n        this.maxPoint = maxPoint;\n    }\n    /**\n    @internal\n    */\n    static create(chunkPos, chunk, nextLayer, maxPoint) {\n        return new RangeSet(chunkPos, chunk, nextLayer, maxPoint);\n    }\n    /**\n    @internal\n    */\n    get length() {\n        let last = this.chunk.length - 1;\n        return last < 0 ? 0 : Math.max(this.chunkEnd(last), this.nextLayer.length);\n    }\n    /**\n    The number of ranges in the set.\n    */\n    get size() {\n        if (this.isEmpty)\n            return 0;\n        let size = this.nextLayer.size;\n        for (let chunk of this.chunk)\n            size += chunk.value.length;\n        return size;\n    }\n    /**\n    @internal\n    */\n    chunkEnd(index) {\n        return this.chunkPos[index] + this.chunk[index].length;\n    }\n    /**\n    Update the range set, optionally adding new ranges or filtering\n    out existing ones.\n    \n    (Note: The type parameter is just there as a kludge to work\n    around TypeScript variance issues that prevented `RangeSet<X>`\n    from being a subtype of `RangeSet<Y>` when `X` is a subtype of\n    `Y`.)\n    */\n    update(updateSpec) {\n        let { add = [], sort = false, filterFrom = 0, filterTo = this.length } = updateSpec;\n        let filter = updateSpec.filter;\n        if (add.length == 0 && !filter)\n            return this;\n        if (sort)\n            add = add.slice().sort(cmpRange);\n        if (this.isEmpty)\n            return add.length ? RangeSet.of(add) : this;\n        let cur = new LayerCursor(this, null, -1).goto(0), i = 0, spill = [];\n        let builder = new RangeSetBuilder();\n        while (cur.value || i < add.length) {\n            if (i < add.length && (cur.from - add[i].from || cur.startSide - add[i].value.startSide) >= 0) {\n                let range = add[i++];\n                if (!builder.addInner(range.from, range.to, range.value))\n                    spill.push(range);\n            }\n            else if (cur.rangeIndex == 1 && cur.chunkIndex < this.chunk.length &&\n                (i == add.length || this.chunkEnd(cur.chunkIndex) < add[i].from) &&\n                (!filter || filterFrom > this.chunkEnd(cur.chunkIndex) || filterTo < this.chunkPos[cur.chunkIndex]) &&\n                builder.addChunk(this.chunkPos[cur.chunkIndex], this.chunk[cur.chunkIndex])) {\n                cur.nextChunk();\n            }\n            else {\n                if (!filter || filterFrom > cur.to || filterTo < cur.from || filter(cur.from, cur.to, cur.value)) {\n                    if (!builder.addInner(cur.from, cur.to, cur.value))\n                        spill.push(Range.create(cur.from, cur.to, cur.value));\n                }\n                cur.next();\n            }\n        }\n        return builder.finishInner(this.nextLayer.isEmpty && !spill.length ? RangeSet.empty\n            : this.nextLayer.update({ add: spill, filter, filterFrom, filterTo }));\n    }\n    /**\n    Map this range set through a set of changes, return the new set.\n    */\n    map(changes) {\n        if (changes.empty || this.isEmpty)\n            return this;\n        let chunks = [], chunkPos = [], maxPoint = -1;\n        for (let i = 0; i < this.chunk.length; i++) {\n            let start = this.chunkPos[i], chunk = this.chunk[i];\n            let touch = changes.touchesRange(start, start + chunk.length);\n            if (touch === false) {\n                maxPoint = Math.max(maxPoint, chunk.maxPoint);\n                chunks.push(chunk);\n                chunkPos.push(changes.mapPos(start));\n            }\n            else if (touch === true) {\n                let { mapped, pos } = chunk.map(start, changes);\n                if (mapped) {\n                    maxPoint = Math.max(maxPoint, mapped.maxPoint);\n                    chunks.push(mapped);\n                    chunkPos.push(pos);\n                }\n            }\n        }\n        let next = this.nextLayer.map(changes);\n        return chunks.length == 0 ? next : new RangeSet(chunkPos, chunks, next || RangeSet.empty, maxPoint);\n    }\n    /**\n    Iterate over the ranges that touch the region `from` to `to`,\n    calling `f` for each. There is no guarantee that the ranges will\n    be reported in any specific order. When the callback returns\n    `false`, iteration stops.\n    */\n    between(from, to, f) {\n        if (this.isEmpty)\n            return;\n        for (let i = 0; i < this.chunk.length; i++) {\n            let start = this.chunkPos[i], chunk = this.chunk[i];\n            if (to >= start && from <= start + chunk.length &&\n                chunk.between(start, from - start, to - start, f) === false)\n                return;\n        }\n        this.nextLayer.between(from, to, f);\n    }\n    /**\n    Iterate over the ranges in this set, in order, including all\n    ranges that end at or after `from`.\n    */\n    iter(from = 0) {\n        return HeapCursor.from([this]).goto(from);\n    }\n    /**\n    @internal\n    */\n    get isEmpty() { return this.nextLayer == this; }\n    /**\n    Iterate over the ranges in a collection of sets, in order,\n    starting from `from`.\n    */\n    static iter(sets, from = 0) {\n        return HeapCursor.from(sets).goto(from);\n    }\n    /**\n    Iterate over two groups of sets, calling methods on `comparator`\n    to notify it of possible differences.\n    */\n    static compare(oldSets, newSets, \n    /**\n    This indicates how the underlying data changed between these\n    ranges, and is needed to synchronize the iteration.\n    */\n    textDiff, comparator, \n    /**\n    Can be used to ignore all non-point ranges, and points below\n    the given size. When -1, all ranges are compared.\n    */\n    minPointSize = -1) {\n        let a = oldSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n        let b = newSets.filter(set => set.maxPoint > 0 || !set.isEmpty && set.maxPoint >= minPointSize);\n        let sharedChunks = findSharedChunks(a, b, textDiff);\n        let sideA = new SpanCursor(a, sharedChunks, minPointSize);\n        let sideB = new SpanCursor(b, sharedChunks, minPointSize);\n        textDiff.iterGaps((fromA, fromB, length) => compare(sideA, fromA, sideB, fromB, length, comparator));\n        if (textDiff.empty && textDiff.length == 0)\n            compare(sideA, 0, sideB, 0, 0, comparator);\n    }\n    /**\n    Compare the contents of two groups of range sets, returning true\n    if they are equivalent in the given range.\n    */\n    static eq(oldSets, newSets, from = 0, to) {\n        if (to == null)\n            to = ********** /* C.Far */ - 1;\n        let a = oldSets.filter(set => !set.isEmpty && newSets.indexOf(set) < 0);\n        let b = newSets.filter(set => !set.isEmpty && oldSets.indexOf(set) < 0);\n        if (a.length != b.length)\n            return false;\n        if (!a.length)\n            return true;\n        let sharedChunks = findSharedChunks(a, b);\n        let sideA = new SpanCursor(a, sharedChunks, 0).goto(from), sideB = new SpanCursor(b, sharedChunks, 0).goto(from);\n        for (;;) {\n            if (sideA.to != sideB.to ||\n                !sameValues(sideA.active, sideB.active) ||\n                sideA.point && (!sideB.point || !sideA.point.eq(sideB.point)))\n                return false;\n            if (sideA.to > to)\n                return true;\n            sideA.next();\n            sideB.next();\n        }\n    }\n    /**\n    Iterate over a group of range sets at the same time, notifying\n    the iterator about the ranges covering every given piece of\n    content. Returns the open count (see\n    [`SpanIterator.span`](https://codemirror.net/6/docs/ref/#state.SpanIterator.span)) at the end\n    of the iteration.\n    */\n    static spans(sets, from, to, iterator, \n    /**\n    When given and greater than -1, only points of at least this\n    size are taken into account.\n    */\n    minPointSize = -1) {\n        let cursor = new SpanCursor(sets, null, minPointSize).goto(from), pos = from;\n        let openRanges = cursor.openStart;\n        for (;;) {\n            let curTo = Math.min(cursor.to, to);\n            if (cursor.point) {\n                let active = cursor.activeForPoint(cursor.to);\n                let openCount = cursor.pointFrom < from ? active.length + 1\n                    : cursor.point.startSide < 0 ? active.length\n                        : Math.min(active.length, openRanges);\n                iterator.point(pos, curTo, cursor.point, active, openCount, cursor.pointRank);\n                openRanges = Math.min(cursor.openEnd(curTo), active.length);\n            }\n            else if (curTo > pos) {\n                iterator.span(pos, curTo, cursor.active, openRanges);\n                openRanges = cursor.openEnd(curTo);\n            }\n            if (cursor.to > to)\n                return openRanges + (cursor.point && cursor.to > to ? 1 : 0);\n            pos = cursor.to;\n            cursor.next();\n        }\n    }\n    /**\n    Create a range set for the given range or array of ranges. By\n    default, this expects the ranges to be _sorted_ (by start\n    position and, if two start at the same position,\n    `value.startSide`). You can pass `true` as second argument to\n    cause the method to sort them.\n    */\n    static of(ranges, sort = false) {\n        let build = new RangeSetBuilder();\n        for (let range of ranges instanceof Range ? [ranges] : sort ? lazySort(ranges) : ranges)\n            build.add(range.from, range.to, range.value);\n        return build.finish();\n    }\n    /**\n    Join an array of range sets into a single set.\n    */\n    static join(sets) {\n        if (!sets.length)\n            return RangeSet.empty;\n        let result = sets[sets.length - 1];\n        for (let i = sets.length - 2; i >= 0; i--) {\n            for (let layer = sets[i]; layer != RangeSet.empty; layer = layer.nextLayer)\n                result = new RangeSet(layer.chunkPos, layer.chunk, result, Math.max(layer.maxPoint, result.maxPoint));\n        }\n        return result;\n    }\n}\n/**\nThe empty set of ranges.\n*/\nRangeSet.empty = /*@__PURE__*/new RangeSet([], [], null, -1);\nfunction lazySort(ranges) {\n    if (ranges.length > 1)\n        for (let prev = ranges[0], i = 1; i < ranges.length; i++) {\n            let cur = ranges[i];\n            if (cmpRange(prev, cur) > 0)\n                return ranges.slice().sort(cmpRange);\n            prev = cur;\n        }\n    return ranges;\n}\nRangeSet.empty.nextLayer = RangeSet.empty;\n/**\nA range set builder is a data structure that helps build up a\n[range set](https://codemirror.net/6/docs/ref/#state.RangeSet) directly, without first allocating\nan array of [`Range`](https://codemirror.net/6/docs/ref/#state.Range) objects.\n*/\nclass RangeSetBuilder {\n    finishChunk(newArrays) {\n        this.chunks.push(new Chunk(this.from, this.to, this.value, this.maxPoint));\n        this.chunkPos.push(this.chunkStart);\n        this.chunkStart = -1;\n        this.setMaxPoint = Math.max(this.setMaxPoint, this.maxPoint);\n        this.maxPoint = -1;\n        if (newArrays) {\n            this.from = [];\n            this.to = [];\n            this.value = [];\n        }\n    }\n    /**\n    Create an empty builder.\n    */\n    constructor() {\n        this.chunks = [];\n        this.chunkPos = [];\n        this.chunkStart = -1;\n        this.last = null;\n        this.lastFrom = -********** /* C.Far */;\n        this.lastTo = -********** /* C.Far */;\n        this.from = [];\n        this.to = [];\n        this.value = [];\n        this.maxPoint = -1;\n        this.setMaxPoint = -1;\n        this.nextLayer = null;\n    }\n    /**\n    Add a range. Ranges should be added in sorted (by `from` and\n    `value.startSide`) order.\n    */\n    add(from, to, value) {\n        if (!this.addInner(from, to, value))\n            (this.nextLayer || (this.nextLayer = new RangeSetBuilder)).add(from, to, value);\n    }\n    /**\n    @internal\n    */\n    addInner(from, to, value) {\n        let diff = from - this.lastTo || value.startSide - this.last.endSide;\n        if (diff <= 0 && (from - this.lastFrom || value.startSide - this.last.startSide) < 0)\n            throw new Error(\"Ranges must be added sorted by `from` position and `startSide`\");\n        if (diff < 0)\n            return false;\n        if (this.from.length == 250 /* C.ChunkSize */)\n            this.finishChunk(true);\n        if (this.chunkStart < 0)\n            this.chunkStart = from;\n        this.from.push(from - this.chunkStart);\n        this.to.push(to - this.chunkStart);\n        this.last = value;\n        this.lastFrom = from;\n        this.lastTo = to;\n        this.value.push(value);\n        if (value.point)\n            this.maxPoint = Math.max(this.maxPoint, to - from);\n        return true;\n    }\n    /**\n    @internal\n    */\n    addChunk(from, chunk) {\n        if ((from - this.lastTo || chunk.value[0].startSide - this.last.endSide) < 0)\n            return false;\n        if (this.from.length)\n            this.finishChunk(true);\n        this.setMaxPoint = Math.max(this.setMaxPoint, chunk.maxPoint);\n        this.chunks.push(chunk);\n        this.chunkPos.push(from);\n        let last = chunk.value.length - 1;\n        this.last = chunk.value[last];\n        this.lastFrom = chunk.from[last] + from;\n        this.lastTo = chunk.to[last] + from;\n        return true;\n    }\n    /**\n    Finish the range set. Returns the new set. The builder can't be\n    used anymore after this has been called.\n    */\n    finish() { return this.finishInner(RangeSet.empty); }\n    /**\n    @internal\n    */\n    finishInner(next) {\n        if (this.from.length)\n            this.finishChunk(false);\n        if (this.chunks.length == 0)\n            return next;\n        let result = RangeSet.create(this.chunkPos, this.chunks, this.nextLayer ? this.nextLayer.finishInner(next) : next, this.setMaxPoint);\n        this.from = null; // Make sure further `add` calls produce errors\n        return result;\n    }\n}\nfunction findSharedChunks(a, b, textDiff) {\n    let inA = new Map();\n    for (let set of a)\n        for (let i = 0; i < set.chunk.length; i++)\n            if (set.chunk[i].maxPoint <= 0)\n                inA.set(set.chunk[i], set.chunkPos[i]);\n    let shared = new Set();\n    for (let set of b)\n        for (let i = 0; i < set.chunk.length; i++) {\n            let known = inA.get(set.chunk[i]);\n            if (known != null && (textDiff ? textDiff.mapPos(known) : known) == set.chunkPos[i] &&\n                !(textDiff === null || textDiff === void 0 ? void 0 : textDiff.touchesRange(known, known + set.chunk[i].length)))\n                shared.add(set.chunk[i]);\n        }\n    return shared;\n}\nclass LayerCursor {\n    constructor(layer, skip, minPoint, rank = 0) {\n        this.layer = layer;\n        this.skip = skip;\n        this.minPoint = minPoint;\n        this.rank = rank;\n    }\n    get startSide() { return this.value ? this.value.startSide : 0; }\n    get endSide() { return this.value ? this.value.endSide : 0; }\n    goto(pos, side = -********** /* C.Far */) {\n        this.chunkIndex = this.rangeIndex = 0;\n        this.gotoInner(pos, side, false);\n        return this;\n    }\n    gotoInner(pos, side, forward) {\n        while (this.chunkIndex < this.layer.chunk.length) {\n            let next = this.layer.chunk[this.chunkIndex];\n            if (!(this.skip && this.skip.has(next) ||\n                this.layer.chunkEnd(this.chunkIndex) < pos ||\n                next.maxPoint < this.minPoint))\n                break;\n            this.chunkIndex++;\n            forward = false;\n        }\n        if (this.chunkIndex < this.layer.chunk.length) {\n            let rangeIndex = this.layer.chunk[this.chunkIndex].findIndex(pos - this.layer.chunkPos[this.chunkIndex], side, true);\n            if (!forward || this.rangeIndex < rangeIndex)\n                this.setRangeIndex(rangeIndex);\n        }\n        this.next();\n    }\n    forward(pos, side) {\n        if ((this.to - pos || this.endSide - side) < 0)\n            this.gotoInner(pos, side, true);\n    }\n    next() {\n        for (;;) {\n            if (this.chunkIndex == this.layer.chunk.length) {\n                this.from = this.to = ********** /* C.Far */;\n                this.value = null;\n                break;\n            }\n            else {\n                let chunkPos = this.layer.chunkPos[this.chunkIndex], chunk = this.layer.chunk[this.chunkIndex];\n                let from = chunkPos + chunk.from[this.rangeIndex];\n                this.from = from;\n                this.to = chunkPos + chunk.to[this.rangeIndex];\n                this.value = chunk.value[this.rangeIndex];\n                this.setRangeIndex(this.rangeIndex + 1);\n                if (this.minPoint < 0 || this.value.point && this.to - this.from >= this.minPoint)\n                    break;\n            }\n        }\n    }\n    setRangeIndex(index) {\n        if (index == this.layer.chunk[this.chunkIndex].value.length) {\n            this.chunkIndex++;\n            if (this.skip) {\n                while (this.chunkIndex < this.layer.chunk.length && this.skip.has(this.layer.chunk[this.chunkIndex]))\n                    this.chunkIndex++;\n            }\n            this.rangeIndex = 0;\n        }\n        else {\n            this.rangeIndex = index;\n        }\n    }\n    nextChunk() {\n        this.chunkIndex++;\n        this.rangeIndex = 0;\n        this.next();\n    }\n    compare(other) {\n        return this.from - other.from || this.startSide - other.startSide || this.rank - other.rank ||\n            this.to - other.to || this.endSide - other.endSide;\n    }\n}\nclass HeapCursor {\n    constructor(heap) {\n        this.heap = heap;\n    }\n    static from(sets, skip = null, minPoint = -1) {\n        let heap = [];\n        for (let i = 0; i < sets.length; i++) {\n            for (let cur = sets[i]; !cur.isEmpty; cur = cur.nextLayer) {\n                if (cur.maxPoint >= minPoint)\n                    heap.push(new LayerCursor(cur, skip, minPoint, i));\n            }\n        }\n        return heap.length == 1 ? heap[0] : new HeapCursor(heap);\n    }\n    get startSide() { return this.value ? this.value.startSide : 0; }\n    goto(pos, side = -********** /* C.Far */) {\n        for (let cur of this.heap)\n            cur.goto(pos, side);\n        for (let i = this.heap.length >> 1; i >= 0; i--)\n            heapBubble(this.heap, i);\n        this.next();\n        return this;\n    }\n    forward(pos, side) {\n        for (let cur of this.heap)\n            cur.forward(pos, side);\n        for (let i = this.heap.length >> 1; i >= 0; i--)\n            heapBubble(this.heap, i);\n        if ((this.to - pos || this.value.endSide - side) < 0)\n            this.next();\n    }\n    next() {\n        if (this.heap.length == 0) {\n            this.from = this.to = ********** /* C.Far */;\n            this.value = null;\n            this.rank = -1;\n        }\n        else {\n            let top = this.heap[0];\n            this.from = top.from;\n            this.to = top.to;\n            this.value = top.value;\n            this.rank = top.rank;\n            if (top.value)\n                top.next();\n            heapBubble(this.heap, 0);\n        }\n    }\n}\nfunction heapBubble(heap, index) {\n    for (let cur = heap[index];;) {\n        let childIndex = (index << 1) + 1;\n        if (childIndex >= heap.length)\n            break;\n        let child = heap[childIndex];\n        if (childIndex + 1 < heap.length && child.compare(heap[childIndex + 1]) >= 0) {\n            child = heap[childIndex + 1];\n            childIndex++;\n        }\n        if (cur.compare(child) < 0)\n            break;\n        heap[childIndex] = cur;\n        heap[index] = child;\n        index = childIndex;\n    }\n}\nclass SpanCursor {\n    constructor(sets, skip, minPoint) {\n        this.minPoint = minPoint;\n        this.active = [];\n        this.activeTo = [];\n        this.activeRank = [];\n        this.minActive = -1;\n        // A currently active point range, if any\n        this.point = null;\n        this.pointFrom = 0;\n        this.pointRank = 0;\n        this.to = -********** /* C.Far */;\n        this.endSide = 0;\n        // The amount of open active ranges at the start of the iterator.\n        // Not including points.\n        this.openStart = -1;\n        this.cursor = HeapCursor.from(sets, skip, minPoint);\n    }\n    goto(pos, side = -********** /* C.Far */) {\n        this.cursor.goto(pos, side);\n        this.active.length = this.activeTo.length = this.activeRank.length = 0;\n        this.minActive = -1;\n        this.to = pos;\n        this.endSide = side;\n        this.openStart = -1;\n        this.next();\n        return this;\n    }\n    forward(pos, side) {\n        while (this.minActive > -1 && (this.activeTo[this.minActive] - pos || this.active[this.minActive].endSide - side) < 0)\n            this.removeActive(this.minActive);\n        this.cursor.forward(pos, side);\n    }\n    removeActive(index) {\n        remove(this.active, index);\n        remove(this.activeTo, index);\n        remove(this.activeRank, index);\n        this.minActive = findMinIndex(this.active, this.activeTo);\n    }\n    addActive(trackOpen) {\n        let i = 0, { value, to, rank } = this.cursor;\n        // Organize active marks by rank first, then by size\n        while (i < this.activeRank.length && (rank - this.activeRank[i] || to - this.activeTo[i]) > 0)\n            i++;\n        insert(this.active, i, value);\n        insert(this.activeTo, i, to);\n        insert(this.activeRank, i, rank);\n        if (trackOpen)\n            insert(trackOpen, i, this.cursor.from);\n        this.minActive = findMinIndex(this.active, this.activeTo);\n    }\n    // After calling this, if `this.point` != null, the next range is a\n    // point. Otherwise, it's a regular range, covered by `this.active`.\n    next() {\n        let from = this.to, wasPoint = this.point;\n        this.point = null;\n        let trackOpen = this.openStart < 0 ? [] : null;\n        for (;;) {\n            let a = this.minActive;\n            if (a > -1 && (this.activeTo[a] - this.cursor.from || this.active[a].endSide - this.cursor.startSide) < 0) {\n                if (this.activeTo[a] > from) {\n                    this.to = this.activeTo[a];\n                    this.endSide = this.active[a].endSide;\n                    break;\n                }\n                this.removeActive(a);\n                if (trackOpen)\n                    remove(trackOpen, a);\n            }\n            else if (!this.cursor.value) {\n                this.to = this.endSide = ********** /* C.Far */;\n                break;\n            }\n            else if (this.cursor.from > from) {\n                this.to = this.cursor.from;\n                this.endSide = this.cursor.startSide;\n                break;\n            }\n            else {\n                let nextVal = this.cursor.value;\n                if (!nextVal.point) { // Opening a range\n                    this.addActive(trackOpen);\n                    this.cursor.next();\n                }\n                else if (wasPoint && this.cursor.to == this.to && this.cursor.from < this.cursor.to) {\n                    // Ignore any non-empty points that end precisely at the end of the prev point\n                    this.cursor.next();\n                }\n                else { // New point\n                    this.point = nextVal;\n                    this.pointFrom = this.cursor.from;\n                    this.pointRank = this.cursor.rank;\n                    this.to = this.cursor.to;\n                    this.endSide = nextVal.endSide;\n                    this.cursor.next();\n                    this.forward(this.to, this.endSide);\n                    break;\n                }\n            }\n        }\n        if (trackOpen) {\n            this.openStart = 0;\n            for (let i = trackOpen.length - 1; i >= 0 && trackOpen[i] < from; i--)\n                this.openStart++;\n        }\n    }\n    activeForPoint(to) {\n        if (!this.active.length)\n            return this.active;\n        let active = [];\n        for (let i = this.active.length - 1; i >= 0; i--) {\n            if (this.activeRank[i] < this.pointRank)\n                break;\n            if (this.activeTo[i] > to || this.activeTo[i] == to && this.active[i].endSide >= this.point.endSide)\n                active.push(this.active[i]);\n        }\n        return active.reverse();\n    }\n    openEnd(to) {\n        let open = 0;\n        for (let i = this.activeTo.length - 1; i >= 0 && this.activeTo[i] > to; i--)\n            open++;\n        return open;\n    }\n}\nfunction compare(a, startA, b, startB, length, comparator) {\n    a.goto(startA);\n    b.goto(startB);\n    let endB = startB + length;\n    let pos = startB, dPos = startB - startA;\n    for (;;) {\n        let dEnd = (a.to + dPos) - b.to, diff = dEnd || a.endSide - b.endSide;\n        let end = diff < 0 ? a.to + dPos : b.to, clipEnd = Math.min(end, endB);\n        if (a.point || b.point) {\n            if (!(a.point && b.point && (a.point == b.point || a.point.eq(b.point)) &&\n                sameValues(a.activeForPoint(a.to), b.activeForPoint(b.to))))\n                comparator.comparePoint(pos, clipEnd, a.point, b.point);\n        }\n        else {\n            if (clipEnd > pos && !sameValues(a.active, b.active))\n                comparator.compareRange(pos, clipEnd, a.active, b.active);\n        }\n        if (end > endB)\n            break;\n        if ((dEnd || a.openEnd != b.openEnd) && comparator.boundChange)\n            comparator.boundChange(end);\n        pos = end;\n        if (diff <= 0)\n            a.next();\n        if (diff >= 0)\n            b.next();\n    }\n}\nfunction sameValues(a, b) {\n    if (a.length != b.length)\n        return false;\n    for (let i = 0; i < a.length; i++)\n        if (a[i] != b[i] && !a[i].eq(b[i]))\n            return false;\n    return true;\n}\nfunction remove(array, index) {\n    for (let i = index, e = array.length - 1; i < e; i++)\n        array[i] = array[i + 1];\n    array.pop();\n}\nfunction insert(array, index, value) {\n    for (let i = array.length - 1; i >= index; i--)\n        array[i + 1] = array[i];\n    array[index] = value;\n}\nfunction findMinIndex(value, array) {\n    let found = -1, foundPos = ********** /* C.Far */;\n    for (let i = 0; i < array.length; i++)\n        if ((array[i] - foundPos || value[i].endSide - value[found].endSide) < 0) {\n            found = i;\n            foundPos = array[i];\n        }\n    return found;\n}\n\n/**\nCount the column position at the given offset into the string,\ntaking extending characters and tab size into account.\n*/\nfunction countColumn(string, tabSize, to = string.length) {\n    let n = 0;\n    for (let i = 0; i < to && i < string.length;) {\n        if (string.charCodeAt(i) == 9) {\n            n += tabSize - (n % tabSize);\n            i++;\n        }\n        else {\n            n++;\n            i = findClusterBreak(string, i);\n        }\n    }\n    return n;\n}\n/**\nFind the offset that corresponds to the given column position in a\nstring, taking extending characters and tab size into account. By\ndefault, the string length is returned when it is too short to\nreach the column. Pass `strict` true to make it return -1 in that\nsituation.\n*/\nfunction findColumn(string, col, tabSize, strict) {\n    for (let i = 0, n = 0;;) {\n        if (n >= col)\n            return i;\n        if (i == string.length)\n            break;\n        n += string.charCodeAt(i) == 9 ? tabSize - (n % tabSize) : 1;\n        i = findClusterBreak(string, i);\n    }\n    return strict === true ? -1 : string.length;\n}\n\nexport { Annotation, AnnotationType, ChangeDesc, ChangeSet, CharCategory, Compartment, EditorSelection, EditorState, Facet, Line, MapMode, Prec, Range, RangeSet, RangeSetBuilder, RangeValue, SelectionRange, StateEffect, StateEffectType, StateField, Text, Transaction, codePointAt, codePointSize, combineConfig, countColumn, findClusterBreak, findColumn, fromCodePoint };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA;;AAEA,GACA,MAAM;IACF;;IAEA,GACA,OAAO,GAAG,EAAE;QACR,IAAI,MAAM,KAAK,MAAM,IAAI,CAAC,MAAM,EAC5B,MAAM,IAAI,WAAW,CAAC,iBAAiB,EAAE,IAAI,uBAAuB,EAAE,IAAI,CAAC,MAAM,EAAE;QACvF,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,OAAO,GAAG;IACzC;IACA;;IAEA,GACA,KAAK,CAAC,EAAE;QACJ,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,EACvB,MAAM,IAAI,WAAW,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;QAClF,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,GAAG;IACtC;IACA;;IAEA,GACA,QAAQ,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;QACpB,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE,MAAM;QAC9B,IAAI,QAAQ,EAAE;QACd,IAAI,CAAC,SAAS,CAAC,GAAG,MAAM,OAAO,EAAE,WAAW;QAC5C,IAAI,KAAK,MAAM,EACX,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,EAAE,OAAO,EAAE,aAAa,MAAK,EAAE,WAAW;QAC3E,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,aAAa;QACtD,OAAO,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM;IACvE;IACA;;IAEA,GACA,OAAO,KAAK,EAAE;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE;IAClD;IACA;;IAEA,GACA,MAAM,IAAI,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE;QAC1B,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE,MAAM;QAC9B,IAAI,QAAQ,EAAE;QACd,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,OAAO;QAChC,OAAO,SAAS,IAAI,CAAC,OAAO,KAAK;IACrC;IACA;;IAEA,GACA,GAAG,KAAK,EAAE;QACN,IAAI,SAAS,IAAI,EACb,OAAO;QACX,IAAI,MAAM,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,KAAK,EACxD,OAAO;QACX,IAAI,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;QACzF,IAAI,IAAI,IAAI,cAAc,IAAI,GAAG,IAAI,IAAI,cAAc;QACvD,IAAK,IAAI,OAAO,OAAO,MAAM,QAAS;YAClC,EAAE,IAAI,CAAC;YACP,EAAE,IAAI,CAAC;YACP,OAAO;YACP,IAAI,EAAE,SAAS,IAAI,EAAE,SAAS,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,EACpE,OAAO;YACX,OAAO,EAAE,KAAK,CAAC,MAAM;YACrB,IAAI,EAAE,IAAI,IAAI,OAAO,KACjB,OAAO;QACf;IACJ;IACA;;;;IAIA,GACA,KAAK,MAAM,CAAC,EAAE;QAAE,OAAO,IAAI,cAAc,IAAI,EAAE;IAAM;IACrD;;;IAGA,GACA,UAAU,IAAI,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE;QAAE,OAAO,IAAI,kBAAkB,IAAI,EAAE,MAAM;IAAK;IAClF;;;;;;IAMA,GACA,UAAU,IAAI,EAAE,EAAE,EAAE;QAChB,IAAI;QACJ,IAAI,QAAQ,MAAM;YACd,QAAQ,IAAI,CAAC,IAAI;QACrB,OACK;YACD,IAAI,MAAM,MACN,KAAK,IAAI,CAAC,KAAK,GAAG;YACtB,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI;YAChC,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,EAAE;QACzH;QACA,OAAO,IAAI,WAAW;IAC1B;IACA;;;IAGA,GACA,WAAW;QAAE,OAAO,IAAI,CAAC,WAAW,CAAC;IAAI;IACzC;;;IAGA,GACA,SAAS;QACL,IAAI,QAAQ,EAAE;QACd,IAAI,CAAC,OAAO,CAAC;QACb,OAAO;IACX;IACA;;IAEA,GACA,aAAc,CAAE;IAChB;;IAEA,GACA,OAAO,GAAG,IAAI,EAAE;QACZ,IAAI,KAAK,MAAM,IAAI,GACf,MAAM,IAAI,WAAW;QACzB,IAAI,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAC5B,OAAO,KAAK,KAAK;QACrB,OAAO,KAAK,MAAM,IAAI,GAAG,eAAe,MAAK,IAAI,SAAS,QAAQ,SAAS,IAAI,CAAC,SAAS,KAAK,CAAC,MAAM,EAAE;IAC3G;AACJ;AACA,sEAAsE;AACtE,mEAAmE;AACnE,wDAAwD;AACxD,MAAM,iBAAiB;IACnB,YAAY,IAAI,EAAE,SAAS,WAAW,KAAK,CAAE;QACzC,KAAK;QACL,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;IAAE;IACvC,IAAI,WAAW;QAAE,OAAO;IAAM;IAC9B,UAAU,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;QACpC,IAAK,IAAI,IAAI,IAAI,IAAK;YAClB,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,SAAS,OAAO,MAAM;YACvD,IAAI,CAAC,SAAS,OAAO,GAAG,KAAK,QACzB,OAAO,IAAI,KAAK,QAAQ,KAAK,MAAM;YACvC,SAAS,MAAM;YACf;QACJ;IACJ;IACA,UAAU,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;QAC9B,IAAI,OAAO,QAAQ,KAAK,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,GAC1C,IAAI,SAAS,UAAU,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,GAAG;QAC3F,IAAI,OAAO,EAAE,aAAa,KAAI;YAC1B,IAAI,OAAO,OAAO,GAAG;YACrB,IAAI,SAAS,WAAW,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,MAAM;YACpE,IAAI,OAAO,MAAM,IAAI,GAAG,eAAe,KAAI;gBACvC,OAAO,IAAI,CAAC,IAAI,SAAS,QAAQ,KAAK,MAAM,GAAG,KAAK,MAAM;YAC9D,OACK;gBACD,IAAI,MAAM,OAAO,MAAM,IAAI;gBAC3B,OAAO,IAAI,CAAC,IAAI,SAAS,OAAO,KAAK,CAAC,GAAG,OAAO,IAAI,SAAS,OAAO,KAAK,CAAC;YAC9E;QACJ,OACK;YACD,OAAO,IAAI,CAAC;QAChB;IACJ;IACA,QAAQ,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;QACpB,IAAI,CAAC,CAAC,gBAAgB,QAAQ,GAC1B,OAAO,KAAK,CAAC,QAAQ,MAAM,IAAI;QACnC,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE,MAAM;QAC9B,IAAI,QAAQ,WAAW,IAAI,CAAC,IAAI,EAAE,WAAW,KAAK,IAAI,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE,GAAG,QAAQ;QACxF,IAAI,SAAS,IAAI,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG,CAAC,KAAK,IAAI;QACnD,IAAI,MAAM,MAAM,IAAI,GAAG,eAAe,KAClC,OAAO,IAAI,SAAS,OAAO;QAC/B,OAAO,SAAS,IAAI,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE,GAAG;IACpD;IACA,YAAY,IAAI,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE,UAAU,IAAI,EAAE;QAChD,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE,MAAM;QAC9B,IAAI,SAAS;QACb,IAAK,IAAI,MAAM,GAAG,IAAI,GAAG,OAAO,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,IAAK;YAC7D,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,KAAK,MAAM;YAChD,IAAI,MAAM,QAAQ,GACd,UAAU;YACd,IAAI,OAAO,OAAO,KAAK,KACnB,UAAU,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,OAAO,MAAM,KAAK;YACvD,MAAM,MAAM;QAChB;QACA,OAAO;IACX;IACA,QAAQ,MAAM,EAAE;QACZ,KAAK,IAAI,QAAQ,IAAI,CAAC,IAAI,CACtB,OAAO,IAAI,CAAC;IACpB;IACA,gBAAgB;QAAE,OAAO;IAAG;IAC5B,OAAO,MAAM,IAAI,EAAE,MAAM,EAAE;QACvB,IAAI,OAAO,EAAE,EAAE,MAAM,CAAC;QACtB,KAAK,IAAI,QAAQ,KAAM;YACnB,KAAK,IAAI,CAAC;YACV,OAAO,KAAK,MAAM,GAAG;YACrB,IAAI,KAAK,MAAM,IAAI,GAAG,eAAe,KAAI;gBACrC,OAAO,IAAI,CAAC,IAAI,SAAS,MAAM;gBAC/B,OAAO,EAAE;gBACT,MAAM,CAAC;YACX;QACJ;QACA,IAAI,MAAM,CAAC,GACP,OAAO,IAAI,CAAC,IAAI,SAAS,MAAM;QACnC,OAAO;IACX;AACJ;AACA,oEAAoE;AACpE,qEAAqE;AACrE,sEAAsE;AACtE,6DAA6D;AAC7D,MAAM,iBAAiB;IACnB,YAAY,QAAQ,EAAE,MAAM,CAAE;QAC1B,KAAK;QACL,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,GAAG;QACb,KAAK,IAAI,SAAS,SACd,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK;IACjC;IACA,UAAU,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE;QACpC,IAAK,IAAI,IAAI,IAAI,IAAK;YAClB,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,SAAS,MAAM,MAAM,EAAE,UAAU,OAAO,MAAM,KAAK,GAAG;YAC1F,IAAI,CAAC,SAAS,UAAU,GAAG,KAAK,QAC5B,OAAO,MAAM,SAAS,CAAC,QAAQ,QAAQ,MAAM;YACjD,SAAS,MAAM;YACf,OAAO,UAAU;QACrB;IACJ;IACA,UAAU,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;QAC9B,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,OAAO,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YACjE,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,MAAM,MAAM,MAAM;YACtD,IAAI,QAAQ,OAAO,MAAM,KAAK;gBAC1B,IAAI,YAAY,OAAO,CAAC,CAAC,OAAO,OAAO,EAAE,aAAa,MAAK,CAAC,IAAI,CAAC,OAAO,KAAK,EAAE,WAAW,MAAK,CAAC,CAAC;gBACjG,IAAI,OAAO,QAAQ,OAAO,MAAM,CAAC,WAC7B,OAAO,IAAI,CAAC;qBAEZ,MAAM,SAAS,CAAC,OAAO,KAAK,KAAK,KAAK,QAAQ;YACtD;YACA,MAAM,MAAM;QAChB;IACJ;IACA,QAAQ,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE;QACpB,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE,MAAM;QAC9B,IAAI,KAAK,KAAK,GAAG,IAAI,CAAC,KAAK,EACvB,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAK;YACpD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,MAAM,MAAM,MAAM;YACtD,0DAA0D;YAC1D,4DAA4D;YAC5D,aAAa;YACb,IAAI,QAAQ,OAAO,MAAM,KAAK;gBAC1B,IAAI,UAAU,MAAM,OAAO,CAAC,OAAO,KAAK,KAAK,KAAK;gBAClD,IAAI,aAAa,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK,GAAG,QAAQ,KAAK;gBACzD,IAAI,QAAQ,KAAK,GAAI,cAAe,EAAE,oBAAoB,MAAK,KAC3D,QAAQ,KAAK,GAAI,cAAe,EAAE,oBAAoB,MAAK,GAAK;oBAChE,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK;oBAC9B,IAAI,CAAC,EAAE,GAAG;oBACV,OAAO,IAAI,SAAS,MAAM,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,MAAM;gBACrE;gBACA,OAAO,KAAK,CAAC,QAAQ,KAAK,KAAK;YACnC;YACA,MAAM,MAAM;QAChB;QACJ,OAAO,KAAK,CAAC,QAAQ,MAAM,IAAI;IACnC;IACA,YAAY,IAAI,EAAE,KAAK,IAAI,CAAC,MAAM,EAAE,UAAU,IAAI,EAAE;QAChD,CAAC,MAAM,GAAG,GAAG,KAAK,IAAI,EAAE,MAAM;QAC9B,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,IAAI,IAAK;YACjE,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,MAAM,MAAM,MAAM;YACtD,IAAI,MAAM,QAAQ,GACd,UAAU;YACd,IAAI,OAAO,OAAO,KAAK,KACnB,UAAU,MAAM,WAAW,CAAC,OAAO,KAAK,KAAK,KAAK;YACtD,MAAM,MAAM;QAChB;QACA,OAAO;IACX;IACA,QAAQ,MAAM,EAAE;QACZ,KAAK,IAAI,SAAS,IAAI,CAAC,QAAQ,CAC3B,MAAM,OAAO,CAAC;IACtB;IACA,cAAc,KAAK,EAAE,GAAG,EAAE;QACtB,IAAI,CAAC,CAAC,iBAAiB,QAAQ,GAC3B,OAAO;QACX,IAAI,SAAS;QACb,IAAI,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG,MAAM,IAAI;YAAC;YAAG;YAAG,IAAI,CAAC,QAAQ,CAAC,MAAM;YAAE,MAAM,QAAQ,CAAC,MAAM;SAAC,GAC9E;YAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG;YAAG,MAAM,QAAQ,CAAC,MAAM,GAAG;YAAG,CAAC;YAAG,CAAC;SAAE;QACnE,OAAQ,MAAM,KAAK,MAAM,IAAK;YAC1B,IAAI,MAAM,MAAM,MAAM,IAClB,OAAO;YACX,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,MAAM,MAAM,QAAQ,CAAC,GAAG;YACrD,IAAI,OAAO,KACP,OAAO,SAAS,IAAI,aAAa,CAAC,KAAK;YAC3C,UAAU,IAAI,MAAM,GAAG;QAC3B;IACJ;IACA,OAAO,KAAK,QAAQ,EAAE,SAAS,SAAS,MAAM,CAAC,CAAC,GAAG,KAAO,IAAI,GAAG,MAAM,GAAG,GAAG,CAAC,EAAE,EAAE;QAC9E,IAAI,QAAQ;QACZ,KAAK,IAAI,MAAM,SACX,SAAS,GAAG,KAAK;QACrB,IAAI,QAAQ,GAAG,eAAe,KAAI;YAC9B,IAAI,OAAO,EAAE;YACb,KAAK,IAAI,MAAM,SACX,GAAG,OAAO,CAAC;YACf,OAAO,IAAI,SAAS,MAAM;QAC9B;QACA,IAAI,QAAQ,KAAK,GAAG,CAAC,GAAG,eAAe,KAAI,SAAS,EAAE,oBAAoB,MAAK,WAAW,SAAS,GAAG,WAAW,SAAS;QAC1H,IAAI,UAAU,EAAE,EAAE,eAAe,GAAG,aAAa,CAAC,GAAG,eAAe,EAAE;QACtE,SAAS,IAAI,KAAK;YACd,IAAI;YACJ,IAAI,MAAM,KAAK,GAAG,YAAY,iBAAiB,UAAU;gBACrD,KAAK,IAAI,QAAQ,MAAM,QAAQ,CAC3B,IAAI;YACZ,OACK,IAAI,MAAM,KAAK,GAAG,YAAY,CAAC,eAAe,YAAY,CAAC,YAAY,GAAG;gBAC3E;gBACA,QAAQ,IAAI,CAAC;YACjB,OACK,IAAI,iBAAiB,YAAY,gBAClC,CAAC,OAAO,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,aAAa,YAC1D,MAAM,KAAK,GAAG,KAAK,KAAK,IAAI,GAAG,eAAe,KAAI;gBAClD,gBAAgB,MAAM,KAAK;gBAC3B,cAAc,MAAM,MAAM,GAAG;gBAC7B,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,GAAG,IAAI,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,GAAG,KAAK,MAAM,GAAG,IAAI,MAAM,MAAM;YACrH,OACK;gBACD,IAAI,eAAe,MAAM,KAAK,GAAG,OAC7B;gBACJ,gBAAgB,MAAM,KAAK;gBAC3B,cAAc,MAAM,MAAM,GAAG;gBAC7B,aAAa,IAAI,CAAC;YACtB;QACJ;QACA,SAAS;YACL,IAAI,gBAAgB,GAChB;YACJ,QAAQ,IAAI,CAAC,aAAa,MAAM,IAAI,IAAI,YAAY,CAAC,EAAE,GAAG,SAAS,IAAI,CAAC,cAAc;YACtF,aAAa,CAAC;YACd,eAAe,aAAa,MAAM,GAAG;QACzC;QACA,KAAK,IAAI,SAAS,SACd,IAAI;QACR;QACA,OAAO,QAAQ,MAAM,IAAI,IAAI,OAAO,CAAC,EAAE,GAAG,IAAI,SAAS,SAAS;IACpE;AACJ;AACA,KAAK,KAAK,GAAG,WAAW,GAAE,IAAI,SAAS;IAAC;CAAG,EAAE;AAC7C,SAAS,WAAW,IAAI;IACpB,IAAI,SAAS,CAAC;IACd,KAAK,IAAI,QAAQ,KACb,UAAU,KAAK,MAAM,GAAG;IAC5B,OAAO;AACX;AACA,SAAS,WAAW,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,KAAK,GAAG;IAChD,IAAK,IAAI,MAAM,GAAG,IAAI,GAAG,QAAQ,MAAM,IAAI,KAAK,MAAM,IAAI,OAAO,IAAI,IAAK;QACtE,IAAI,OAAO,IAAI,CAAC,EAAE,EAAE,MAAM,MAAM,KAAK,MAAM;QAC3C,IAAI,OAAO,MAAM;YACb,IAAI,MAAM,IACN,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK;YAC9B,IAAI,MAAM,MACN,OAAO,KAAK,KAAK,CAAC,OAAO;YAC7B,IAAI,OAAO;gBACP,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,IAAI;gBAC7B,QAAQ;YACZ,OAEI,OAAO,IAAI,CAAC;QACpB;QACA,MAAM,MAAM;IAChB;IACA,OAAO;AACX;AACA,SAAS,UAAU,IAAI,EAAE,IAAI,EAAE,EAAE;IAC7B,OAAO,WAAW,MAAM;QAAC;KAAG,EAAE,MAAM;AACxC;AACA,MAAM;IACF,YAAY,IAAI,EAAE,MAAM,CAAC,CAAE;QACvB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG;YAAC;SAAK;QACnB,IAAI,CAAC,OAAO,GAAG;YAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,WAAW,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,CAAC,MAAM,KAAK;SAAE;IAC5G;IACA,UAAU,IAAI,EAAE,GAAG,EAAE;QACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG;QAC7B,OAAS;YACL,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;YAC/B,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,cAAc,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,SAAS,eAAe;YACtF,IAAI,OAAO,eAAe,WAAW,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,QAAQ,CAAC,MAAM;YAC1E,IAAI,UAAU,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG;gBAChC,IAAI,QAAQ,GAAG;oBACX,IAAI,CAAC,IAAI,GAAG;oBACZ,IAAI,CAAC,KAAK,GAAG;oBACb,OAAO,IAAI;gBACf;gBACA,IAAI,MAAM,GACN,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBAC1B,IAAI,CAAC,KAAK,CAAC,GAAG;gBACd,IAAI,CAAC,OAAO,CAAC,GAAG;YACpB,OACK,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,GAAG;gBAC7C,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI;gBACtB,IAAI,QAAQ,GAAG;oBACX,IAAI,CAAC,SAAS,GAAG;oBACjB,IAAI,CAAC,KAAK,GAAG;oBACb,OAAO,IAAI;gBACf;gBACA;YACJ,OACK,IAAI,eAAe,UAAU;gBAC9B,0BAA0B;gBAC1B,IAAI,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBAChD,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI;gBACtB,IAAI,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,OAAO;oBACjC,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI,OAAO,MAAM,IAAI,KAAK,KAAK,CAAC,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;oBACzF,OAAO,IAAI;gBACf;gBACA,QAAQ,KAAK,MAAM;YACvB,OACK;gBACD,IAAI,OAAO,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;gBACpD,IAAI,OAAO,KAAK,MAAM,EAAE;oBACpB,QAAQ,KAAK,MAAM;oBACnB,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI;gBAC1B,OACK;oBACD,IAAI,MAAM,GACN,IAAI,CAAC,OAAO,CAAC,KAAK;oBACtB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,gBAAgB,WAAW,KAAK,IAAI,CAAC,MAAM,GAAG,KAAK,QAAQ,CAAC,MAAM,KAAK;gBAC5G;YACJ;QACJ;IACJ;IACA,KAAK,OAAO,CAAC,EAAE;QACX,IAAI,OAAO,GAAG;YACV,IAAI,CAAC,SAAS,CAAC,CAAC,MAAO,CAAC,IAAI,CAAC,GAAG;YAChC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;QAC5B;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,GAAG;IACxC;AACJ;AACA,MAAM;IACF,YAAY,IAAI,EAAE,KAAK,EAAE,GAAG,CAAE;QAC1B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG,IAAI,cAAc,MAAM,QAAQ,MAAM,CAAC,IAAI;QACzD,IAAI,CAAC,GAAG,GAAG,QAAQ,MAAM,KAAK,MAAM,GAAG;QACvC,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,OAAO;QAC5B,IAAI,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,OAAO;IAC9B;IACA,UAAU,IAAI,EAAE,GAAG,EAAE;QACjB,IAAI,MAAM,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,EAAE,EAAE;YACvD,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,IAAI,GAAG;YACZ,OAAO,IAAI;QACf;QACA,QAAQ,KAAK,GAAG,CAAC,GAAG,MAAM,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;QACvE,IAAI,QAAQ,MAAM,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG;QAC/D,IAAI,OAAO,OACP,OAAO;QACX,SAAS;QACT,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,MAAM,GAAG,IAAI,IAAI;QACpC,IAAI,CAAC,KAAK,GAAG,MAAM,MAAM,IAAI,QAAQ,QAAQ,MAAM,IAAI,MAAM,KAAK,CAAC,MAAM,MAAM,GAAG,SAAS,MAAM,KAAK,CAAC,GAAG;QAC1G,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK;QACvB,OAAO,IAAI;IACf;IACA,KAAK,OAAO,CAAC,EAAE;QACX,IAAI,OAAO,GACP,OAAO,KAAK,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG;aACzC,IAAI,OAAO,GACZ,OAAO,KAAK,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG;QAC5C,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG;IAC/C;IACA,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,KAAK,IAAI;IAAI;AACxE;AACA,MAAM;IACF,YAAY,KAAK,CAAE;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,KAAK,OAAO,CAAC,EAAE;QACX,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QACjD,IAAI,QAAQ,IAAI,CAAC,UAAU,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,UAAU,GAAG;QACtB,OACK,IAAI,MAAM;YACX,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,KAAK,GAAG;QACjB,OACK,IAAI,WAAW;YAChB,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,KAAK,GAAG;YACjB,OACK;gBACD,IAAI,CAAC,UAAU,GAAG;gBAClB,IAAI,CAAC,IAAI;YACb;QACJ,OACK;YACD,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,UAAU,GAAG;QACtB;QACA,OAAO,IAAI;IACf;IACA,IAAI,YAAY;QAAE,OAAO;IAAO;AACpC;AACA,IAAI,OAAO,UAAU,aAAa;IAC9B,KAAK,SAAS,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI,CAAC,IAAI;IAAI;IACpE,cAAc,SAAS,CAAC,OAAO,QAAQ,CAAC,GAAG,kBAAkB,SAAS,CAAC,OAAO,QAAQ,CAAC,GACnF,WAAW,SAAS,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE;AAC3E;AACA;;;AAGA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,IAAI,EACJ;;;IAGA,GACA,EAAE,EACF;;IAEA,GACA,MAAM,EACN;;IAEA,GACA,IAAI,CAAE;QACF,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;;IAEA,GACA,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI;IAAE;AAC/C;AACA,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE;IACxB,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;IACzC,OAAO;QAAC;QAAM,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;KAAK;AAC5D;AAEA;;;;;;;AAOA,GACA,SAAS,iBAAiB,GAAG,EAAE,GAAG,EAAE,UAAU,IAAI,EAAE,mBAAmB,IAAI;IACvE,OAAO,CAAA,GAAA,oKAAA,CAAA,mBAAkB,AAAD,EAAE,KAAK,KAAK,SAAS;AACjD;AACA,SAAS,aAAa,EAAE;IAAI,OAAO,MAAM,UAAU,KAAK;AAAQ;AAChE,SAAS,cAAc,EAAE;IAAI,OAAO,MAAM,UAAU,KAAK;AAAQ;AACjE;;;;AAIA,GACA,SAAS,YAAY,GAAG,EAAE,GAAG;IACzB,IAAI,QAAQ,IAAI,UAAU,CAAC;IAC3B,IAAI,CAAC,cAAc,UAAU,MAAM,KAAK,IAAI,MAAM,EAC9C,OAAO;IACX,IAAI,QAAQ,IAAI,UAAU,CAAC,MAAM;IACjC,IAAI,CAAC,aAAa,QACd,OAAO;IACX,OAAO,CAAC,AAAC,QAAQ,UAAW,EAAE,IAAI,CAAC,QAAQ,MAAM,IAAI;AACzD;AACA;;;;AAIA,GACA,SAAS,cAAc,IAAI;IACvB,IAAI,QAAQ,QACR,OAAO,OAAO,YAAY,CAAC;IAC/B,QAAQ;IACR,OAAO,OAAO,YAAY,CAAC,CAAC,QAAQ,EAAE,IAAI,QAAQ,CAAC,OAAO,IAAI,IAAI;AACtE;AACA;;AAEA,GACA,SAAS,cAAc,IAAI;IAAI,OAAO,OAAO,UAAU,IAAI;AAAG;AAE9D,MAAM,eAAe;AACrB;;AAEA,GACA,IAAI,UAAU,WAAW,GAAE,AAAC,SAAU,OAAO;IACzC;;;IAGA,GACA,OAAO,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,GAAG;IACjC;;IAEA,GACA,OAAO,CAAC,OAAO,CAAC,WAAW,GAAG,EAAE,GAAG;IACnC;;IAEA,GACA,OAAO,CAAC,OAAO,CAAC,cAAc,GAAG,EAAE,GAAG;IACtC;;IAEA,GACA,OAAO,CAAC,OAAO,CAAC,aAAa,GAAG,EAAE,GAAG;IACzC,OAAO;AAAO,EAAG,WAAW,CAAC,UAAU,CAAC,CAAC;AACzC;;;;AAIA,GACA,MAAM;IACF,8DAA8D;IAC9D,2DAA2D;IAC3D,iEAAiE;IACjE,iEAAiE;IACjE,8CAA8C;IAC9C;;IAEA,GACA,YACA;;IAEA,GACA,QAAQ,CAAE;QACN,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;IAEA,GACA,IAAI,SAAS;QACT,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAC3C,UAAU,IAAI,CAAC,QAAQ,CAAC,EAAE;QAC9B,OAAO;IACX;IACA;;IAEA,GACA,IAAI,YAAY;QACZ,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;YAC9C,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YAC9B,UAAU,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;QAC3C;QACA,OAAO;IACX;IACA;;IAEA,GACA,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG;IAAG;IACrG;;;;IAIA,GACA,SAAS,CAAC,EAAE;QACR,IAAK,IAAI,IAAI,GAAG,OAAO,GAAG,OAAO,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAG;YAC3D,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI;YACtD,IAAI,MAAM,GAAG;gBACT,EAAE,MAAM,MAAM;gBACd,QAAQ;YACZ,OACK;gBACD,QAAQ;YACZ;YACA,QAAQ;QACZ;IACJ;IACA;;;;;;;;;;;IAWA,GACA,kBAAkB,CAAC,EAAE,aAAa,KAAK,EAAE;QACrC,YAAY,IAAI,EAAE,GAAG;IACzB;IACA;;IAEA,GACA,IAAI,eAAe;QACf,IAAI,WAAW,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAG;YACvC,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI;YACtD,IAAI,MAAM,GACN,SAAS,IAAI,CAAC,KAAK;iBAEnB,SAAS,IAAI,CAAC,KAAK;QAC3B;QACA,OAAO,IAAI,WAAW;IAC1B;IACA;;;;IAIA,GACA,YAAY,KAAK,EAAE;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,MAAM,KAAK,GAAG,IAAI,GAAG,YAAY,IAAI,EAAE;IAAQ;IAChG;;;;;IAKA,GACA,QAAQ,KAAK,EAAE,SAAS,KAAK,EAAE;QAAE,OAAO,MAAM,KAAK,GAAG,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO;IAAS;IAC1F,OAAO,GAAG,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,QAAQ,MAAM,EAAE;QAC3C,IAAI,OAAO,GAAG,OAAO;QACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAG;YACvC,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,OAAO;YACtE,IAAI,MAAM,GAAG;gBACT,IAAI,OAAO,KACP,OAAO,OAAO,CAAC,MAAM,IAAI;gBAC7B,QAAQ;YACZ,OACK;gBACD,IAAI,QAAQ,QAAQ,MAAM,IAAI,QAAQ,OAClC,CAAC,QAAQ,QAAQ,QAAQ,IAAI,OAAO,OAAO,OAAO,OAC9C,QAAQ,QAAQ,WAAW,IAAI,OAAO,OACtC,QAAQ,QAAQ,UAAU,IAAI,OAAO,GAAG,GAC5C,OAAO;gBACX,IAAI,OAAO,OAAO,QAAQ,OAAO,QAAQ,KAAK,CAAC,KAC3C,OAAO,OAAO,QAAQ,QAAQ,IAAI,OAAO,OAAO;gBACpD,QAAQ;YACZ;YACA,OAAO;QACX;QACA,IAAI,MAAM,MACN,MAAM,IAAI,WAAW,CAAC,SAAS,EAAE,IAAI,yCAAyC,EAAE,MAAM;QAC1F,OAAO;IACX;IACA;;;;IAIA,GACA,aAAa,IAAI,EAAE,KAAK,IAAI,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,OAAO,IAAK;YAC7D,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,MAAM;YACpE,IAAI,OAAO,KAAK,OAAO,MAAM,OAAO,MAChC,OAAO,MAAM,QAAQ,MAAM,KAAK,UAAU;YAC9C,MAAM;QACV;QACA,OAAO;IACX;IACA;;IAEA,GACA,WAAW;QACP,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAG;YACvC,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI;YACtD,UAAU,CAAC,SAAS,MAAM,EAAE,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,MAAM,EAAE;QACpE;QACA,OAAO;IACX;IACA;;IAEA,GACA,SAAS;QAAE,OAAO,IAAI,CAAC,QAAQ;IAAE;IACjC;;;IAGA,GACA,OAAO,SAAS,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,KAAK,MAAM,GAAG,KAAK,KAAK,IAAI,CAAC,CAAA,IAAK,OAAO,KAAK,WACtE,MAAM,IAAI,WAAW;QACzB,OAAO,IAAI,WAAW;IAC1B;IACA;;IAEA,GACA,OAAO,OAAO,QAAQ,EAAE;QAAE,OAAO,IAAI,WAAW;IAAW;AAC/D;AACA;;;;AAIA,GACA,MAAM,kBAAkB;IACpB,YAAY,QAAQ,EACpB;;IAEA,GACA,QAAQ,CAAE;QACN,KAAK,CAAC;QACN,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;;IAGA,GACA,MAAM,GAAG,EAAE;QACP,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,MAAM,EACzB,MAAM,IAAI,WAAW;QACzB,YAAY,IAAI,EAAE,CAAC,OAAO,KAAK,OAAO,MAAM,OAAS,MAAM,IAAI,OAAO,CAAC,OAAO,QAAQ,CAAC,MAAM,KAAK,GAAG,OAAO;QAC5G,OAAO;IACX;IACA,QAAQ,KAAK,EAAE,SAAS,KAAK,EAAE;QAAE,OAAO,OAAO,IAAI,EAAE,OAAO,QAAQ;IAAO;IAC3E;;;;;IAKA,GACA,OAAO,GAAG,EAAE;QACR,IAAI,WAAW,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,WAAW,EAAE;QACnD,IAAK,IAAI,IAAI,GAAG,MAAM,GAAG,IAAI,SAAS,MAAM,EAAE,KAAK,EAAG;YAClD,IAAI,MAAM,QAAQ,CAAC,EAAE,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE;YAC5C,IAAI,OAAO,GAAG;gBACV,QAAQ,CAAC,EAAE,GAAG;gBACd,QAAQ,CAAC,IAAI,EAAE,GAAG;gBAClB,IAAI,QAAQ,KAAK;gBACjB,MAAO,SAAS,MAAM,GAAG,MACrB,SAAS,IAAI,CAAC,KAAK,KAAK;gBAC5B,SAAS,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,MAAM,OAAO,KAAK,KAAK;YAC9D;YACA,OAAO;QACX;QACA,OAAO,IAAI,UAAU,UAAU;IACnC;IACA;;;;;IAKA,GACA,QAAQ,KAAK,EAAE;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,QAAQ,MAAM,KAAK,GAAG,IAAI,GAAG,YAAY,IAAI,EAAE,OAAO;IAAO;IAClG;;;;;;;;;;;;IAYA,GACA,IAAI,KAAK,EAAE,SAAS,KAAK,EAAE;QAAE,OAAO,MAAM,KAAK,GAAG,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,QAAQ;IAAO;IAC5F;;;;;;;;IAQA,GACA,YAAY,CAAC,EAAE,aAAa,KAAK,EAAE;QAC/B,YAAY,IAAI,EAAE,GAAG;IACzB;IACA;;;IAGA,GACA,IAAI,OAAO;QAAE,OAAO,WAAW,MAAM,CAAC,IAAI,CAAC,QAAQ;IAAG;IACtD;;IAEA,GACA,OAAO,MAAM,EAAE;QACX,IAAI,iBAAiB,EAAE,EAAE,iBAAiB,EAAE,EAAE,mBAAmB,EAAE;QACnE,IAAI,OAAO,IAAI,YAAY,IAAI;QAC/B,MAAM,IAAK,IAAI,IAAI,GAAG,MAAM,IAAK;YAC7B,IAAI,OAAO,KAAK,OAAO,MAAM,GAAG,MAAM,MAAM,CAAC,IAAI;YACjD,MAAO,MAAM,QAAQ,OAAO,QAAQ,KAAK,GAAG,IAAI,EAAG;gBAC/C,IAAI,KAAK,IAAI,EACT,MAAM;gBACV,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,EAAE,OAAO;gBACpC,WAAW,kBAAkB,KAAK,CAAC;gBACnC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG;gBAC3D,WAAW,gBAAgB,KAAK;gBAChC,IAAI,MAAM,GACN,UAAU,gBAAgB,gBAAgB,KAAK,IAAI;gBACvD,KAAK,OAAO,CAAC;gBACb,OAAO;YACX;YACA,IAAI,MAAM,MAAM,CAAC,IAAI;YACrB,MAAO,MAAM,IAAK;gBACd,IAAI,KAAK,IAAI,EACT,MAAM;gBACV,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,EAAE,MAAM;gBACnC,WAAW,gBAAgB,KAAK,CAAC;gBACjC,WAAW,kBAAkB,KAAK,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,GAAG;gBACnF,KAAK,OAAO,CAAC;gBACb,OAAO;YACX;QACJ;QACA,OAAO;YAAE,SAAS,IAAI,UAAU,gBAAgB;YAC5C,UAAU,WAAW,MAAM,CAAC;QAAkB;IACtD;IACA;;IAEA,GACA,SAAS;QACL,IAAI,QAAQ,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAG;YAC9C,IAAI,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACtD,IAAI,MAAM,GACN,MAAM,IAAI,CAAC;iBACV,IAAI,OAAO,GACZ,MAAM,IAAI,CAAC;gBAAC;aAAI;iBAEhB,MAAM,IAAI,CAAC;gBAAC;aAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,MAAM;QAC5D;QACA,OAAO;IACX;IACA;;;IAGA,GACA,OAAO,GAAG,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE;QAChC,IAAI,WAAW,EAAE,EAAE,WAAW,EAAE,EAAE,MAAM;QACxC,IAAI,QAAQ;QACZ,SAAS,MAAM,QAAQ,KAAK;YACxB,IAAI,CAAC,SAAS,CAAC,SAAS,MAAM,EAC1B;YACJ,IAAI,MAAM,QACN,WAAW,UAAU,SAAS,KAAK,CAAC;YACxC,IAAI,MAAM,IAAI,UAAU,UAAU;YAClC,QAAQ,QAAQ,MAAM,OAAO,CAAC,IAAI,GAAG,CAAC,UAAU;YAChD,WAAW,EAAE;YACb,WAAW,EAAE;YACb,MAAM;QACV;QACA,SAAS,QAAQ,IAAI;YACjB,IAAI,MAAM,OAAO,CAAC,OAAO;gBACrB,KAAK,IAAI,OAAO,KACZ,QAAQ;YAChB,OACK,IAAI,gBAAgB,WAAW;gBAChC,IAAI,KAAK,MAAM,IAAI,QACf,MAAM,IAAI,WAAW,CAAC,kCAAkC,EAAE,KAAK,MAAM,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;gBAChG;gBACA,QAAQ,QAAQ,MAAM,OAAO,CAAC,KAAK,GAAG,CAAC,UAAU;YACrD,OACK;gBACD,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,GAAG;gBAClC,IAAI,OAAO,MAAM,OAAO,KAAK,KAAK,QAC9B,MAAM,IAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,IAAI,EAAE,GAAG,mBAAmB,EAAE,OAAO,CAAC,CAAC;gBAC7F,IAAI,UAAU,CAAC,SAAS,KAAK,KAAK,GAAG,OAAO,UAAU,WAAW,KAAK,EAAE,CAAC,OAAO,KAAK,CAAC,WAAW,iBAAiB;gBAClH,IAAI,SAAS,QAAQ,MAAM;gBAC3B,IAAI,QAAQ,MAAM,UAAU,GACxB;gBACJ,IAAI,OAAO,KACP;gBACJ,IAAI,OAAO,KACP,WAAW,UAAU,OAAO,KAAK,CAAC;gBACtC,WAAW,UAAU,KAAK,MAAM;gBAChC,UAAU,UAAU,UAAU;gBAC9B,MAAM;YACV;QACJ;QACA,QAAQ;QACR,MAAM,CAAC;QACP,OAAO;IACX;IACA;;IAEA,GACA,OAAO,MAAM,MAAM,EAAE;QACjB,OAAO,IAAI,UAAU,SAAS;YAAC;YAAQ,CAAC;SAAE,GAAG,EAAE,EAAE,EAAE;IACvD;IACA;;;IAGA,GACA,OAAO,SAAS,IAAI,EAAE;QAClB,IAAI,CAAC,MAAM,OAAO,CAAC,OACf,MAAM,IAAI,WAAW;QACzB,IAAI,WAAW,EAAE,EAAE,WAAW,EAAE;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,IAAI,OAAO,IAAI,CAAC,EAAE;YAClB,IAAI,OAAO,QAAQ,UAAU;gBACzB,SAAS,IAAI,CAAC,MAAM,CAAC;YACzB,OACK,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,OAAO,IAAI,CAAC,EAAE,IAAI,YAAY,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,KAAK,OAAO,KAAK,WAAW;gBAC3G,MAAM,IAAI,WAAW;YACzB,OACK,IAAI,KAAK,MAAM,IAAI,GAAG;gBACvB,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YAC3B,OACK;gBACD,MAAO,SAAS,MAAM,GAAG,EACrB,SAAS,IAAI,CAAC,KAAK,KAAK;gBAC5B,QAAQ,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,KAAK,KAAK,CAAC;gBACjC,SAAS,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,MAAM;YAC7C;QACJ;QACA,OAAO,IAAI,UAAU,UAAU;IACnC;IACA;;IAEA,GACA,OAAO,UAAU,QAAQ,EAAE,QAAQ,EAAE;QACjC,OAAO,IAAI,UAAU,UAAU;IACnC;AACJ;AACA,SAAS,WAAW,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,YAAY,KAAK;IACrD,IAAI,OAAO,KAAK,OAAO,GACnB;IACJ,IAAI,OAAO,SAAS,MAAM,GAAG;IAC7B,IAAI,QAAQ,KAAK,OAAO,KAAK,OAAO,QAAQ,CAAC,OAAO,EAAE,EAClD,QAAQ,CAAC,KAAK,IAAI;SACjB,IAAI,QAAQ,KAAK,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI,GAChD,QAAQ,CAAC,OAAO,EAAE,IAAI;SACrB,IAAI,WAAW;QAChB,QAAQ,CAAC,KAAK,IAAI;QAClB,QAAQ,CAAC,OAAO,EAAE,IAAI;IAC1B,OAEI,SAAS,IAAI,CAAC,KAAK;AAC3B;AACA,SAAS,UAAU,MAAM,EAAE,QAAQ,EAAE,KAAK;IACtC,IAAI,MAAM,MAAM,IAAI,GAChB;IACJ,IAAI,QAAQ,AAAC,SAAS,MAAM,GAAG,KAAM;IACrC,IAAI,QAAQ,OAAO,MAAM,EAAE;QACvB,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;IACjE,OACK;QACD,MAAO,OAAO,MAAM,GAAG,MACnB,OAAO,IAAI,CAAC,KAAK,KAAK;QAC1B,OAAO,IAAI,CAAC;IAChB;AACJ;AACA,SAAS,YAAY,IAAI,EAAE,CAAC,EAAE,UAAU;IACpC,IAAI,WAAW,KAAK,QAAQ;IAC5B,IAAK,IAAI,OAAO,GAAG,OAAO,GAAG,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,MAAM,EAAG;QAC3D,IAAI,MAAM,KAAK,QAAQ,CAAC,IAAI,EAAE,MAAM,KAAK,QAAQ,CAAC,IAAI;QACtD,IAAI,MAAM,GAAG;YACT,QAAQ;YACR,QAAQ;QACZ,OACK;YACD,IAAI,OAAO,MAAM,OAAO,MAAM,OAAO,KAAK,KAAK;YAC/C,OAAS;gBACL,QAAQ;gBACR,QAAQ;gBACR,IAAI,OAAO,UACP,OAAO,KAAK,MAAM,CAAC,QAAQ,CAAC,AAAC,IAAI,KAAM,EAAE;gBAC7C,IAAI,cAAc,KAAK,KAAK,QAAQ,CAAC,MAAM,IAAI,KAAK,QAAQ,CAAC,IAAI,EAAE,GAAG,GAClE;gBACJ,MAAM,KAAK,QAAQ,CAAC,IAAI;gBACxB,MAAM,KAAK,QAAQ,CAAC,IAAI;YAC5B;YACA,EAAE,MAAM,MAAM,MAAM,MAAM;YAC1B,OAAO;YACP,OAAO;QACX;IACJ;AACJ;AACA,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,KAAK;IAC7C,iEAAiE;IACjE,+DAA+D;IAC/D,IAAI,WAAW,EAAE,EAAE,SAAS,QAAQ,EAAE,GAAG;IACzC,IAAI,IAAI,IAAI,YAAY,OAAO,IAAI,IAAI,YAAY;IACnD,mEAAmE;IACnE,+DAA+D;IAC/D,+DAA+D;IAC/D,SAAS;IACT,IAAK,IAAI,WAAW,CAAC,IAAK;QACtB,IAAI,EAAE,IAAI,IAAI,EAAE,GAAG,IAAI,EAAE,IAAI,IAAI,EAAE,GAAG,EAAE;YACpC,MAAM,IAAI,MAAM;QACpB,OACK,IAAI,EAAE,GAAG,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,GAAG;YACjC,2CAA2C;YAC3C,IAAI,MAAM,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE,EAAE,GAAG;YAC/B,WAAW,UAAU,KAAK,CAAC;YAC3B,EAAE,OAAO,CAAC;YACV,EAAE,OAAO,CAAC;QACd,OACK,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,GAAG,KAAK,YAAY,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,EAAE,GAAG,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG;YACjH,gEAAgE;YAChE,6DAA6D;YAC7D,iDAAiD;YACjD,IAAI,MAAM,EAAE,GAAG;YACf,WAAW,UAAU,EAAE,GAAG,EAAE,CAAC;YAC7B,MAAO,IAAK;gBACR,IAAI,QAAQ,KAAK,GAAG,CAAC,EAAE,GAAG,EAAE;gBAC5B,IAAI,EAAE,GAAG,IAAI,KAAK,WAAW,EAAE,CAAC,IAAI,EAAE,GAAG,IAAI,OAAO;oBAChD,WAAW,UAAU,GAAG,EAAE,GAAG;oBAC7B,IAAI,QACA,UAAU,QAAQ,UAAU,EAAE,IAAI;oBACtC,WAAW,EAAE,CAAC;gBAClB;gBACA,EAAE,OAAO,CAAC;gBACV,OAAO;YACX;YACA,EAAE,IAAI;QACV,OACK,IAAI,EAAE,GAAG,IAAI,GAAG;YACjB,gEAAgE;YAChE,6CAA6C;YAC7C,IAAI,MAAM,GAAG,OAAO,EAAE,GAAG;YACzB,MAAO,KAAM;gBACT,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG;oBACb,IAAI,QAAQ,KAAK,GAAG,CAAC,MAAM,EAAE,GAAG;oBAChC,OAAO;oBACP,QAAQ;oBACR,EAAE,OAAO,CAAC;gBACd,OACK,IAAI,EAAE,GAAG,IAAI,KAAK,EAAE,GAAG,GAAG,MAAM;oBACjC,QAAQ,EAAE,GAAG;oBACb,EAAE,IAAI;gBACV,OACK;oBACD;gBACJ;YACJ;YACA,WAAW,UAAU,KAAK,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;YACnD,IAAI,UAAU,WAAW,EAAE,CAAC,EACxB,UAAU,QAAQ,UAAU,EAAE,IAAI;YACtC,WAAW,EAAE,CAAC;YACd,EAAE,OAAO,CAAC,EAAE,GAAG,GAAG;QACtB,OACK,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE;YACvB,OAAO,SAAS,UAAU,SAAS,CAAC,UAAU,UAAU,WAAW,MAAM,CAAC;QAC9E,OACK;YACD,MAAM,IAAI,MAAM;QACpB;IACJ;AACJ;AACA,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,QAAQ,KAAK;IAC1C,IAAI,WAAW,EAAE;IACjB,IAAI,SAAS,QAAQ,EAAE,GAAG;IAC1B,IAAI,IAAI,IAAI,YAAY,OAAO,IAAI,IAAI,YAAY;IACnD,IAAK,IAAI,OAAO,QAAS;QACrB,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE;YAClB,OAAO,SAAS,UAAU,SAAS,CAAC,UAAU,UAAU,WAAW,MAAM,CAAC;QAC9E,OACK,IAAI,EAAE,GAAG,IAAI,GAAG;YACjB,WAAW,UAAU,EAAE,GAAG,EAAE,GAAG;YAC/B,EAAE,IAAI;QACV,OACK,IAAI,EAAE,GAAG,IAAI,KAAK,CAAC,EAAE,IAAI,EAAE;YAC5B,WAAW,UAAU,GAAG,EAAE,GAAG,EAAE;YAC/B,IAAI,QACA,UAAU,QAAQ,UAAU,EAAE,IAAI;YACtC,EAAE,IAAI;QACV,OACK,IAAI,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE;YACvB,MAAM,IAAI,MAAM;QACpB,OACK;YACD,IAAI,MAAM,KAAK,GAAG,CAAC,EAAE,IAAI,EAAE,EAAE,GAAG,GAAG,aAAa,SAAS,MAAM;YAC/D,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG;gBACb,IAAI,OAAO,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG;gBAC/C,WAAW,UAAU,KAAK,MAAM;gBAChC,IAAI,UAAU,MACV,UAAU,QAAQ,UAAU,EAAE,IAAI;YAC1C,OACK,IAAI,EAAE,GAAG,IAAI,CAAC,GAAG;gBAClB,WAAW,UAAU,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK;gBAC7C,IAAI,QACA,UAAU,QAAQ,UAAU,EAAE,OAAO,CAAC;YAC9C,OACK;gBACD,WAAW,UAAU,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,EAAE;gBAC3D,IAAI,UAAU,CAAC,EAAE,GAAG,EAChB,UAAU,QAAQ,UAAU,EAAE,IAAI;YAC1C;YACA,OAAO,CAAC,EAAE,GAAG,GAAG,OAAO,EAAE,GAAG,IAAI,KAAK,EAAE,GAAG,GAAG,GAAG,KAAK,CAAC,QAAQ,SAAS,MAAM,GAAG,UAAU;YAC1F,EAAE,QAAQ,CAAC;YACX,EAAE,OAAO,CAAC;QACd;IACJ;AACJ;AACA,MAAM;IACF,YAAY,GAAG,CAAE;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,IAAI;IACb;IACA,OAAO;QACH,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG;QAC3B,IAAI,IAAI,CAAC,CAAC,GAAG,SAAS,MAAM,EAAE;YAC1B,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;YAC7B,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG;QACjC,OACK;YACD,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,GAAG,GAAG,CAAC;QAChB;QACA,IAAI,CAAC,GAAG,GAAG;IACf;IACA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC;IAAG;IACpC,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG;IAAE;IACxD,IAAI,OAAO;QACP,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,QAAQ,AAAC,IAAI,CAAC,CAAC,GAAG,KAAM;QACrD,OAAO,SAAS,SAAS,MAAM,GAAG,KAAK,KAAK,GAAG,QAAQ,CAAC,MAAM;IAClE;IACA,QAAQ,GAAG,EAAE;QACT,IAAI,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,QAAQ,AAAC,IAAI,CAAC,CAAC,GAAG,KAAM;QACrD,OAAO,SAAS,SAAS,MAAM,IAAI,CAAC,MAAM,KAAK,KAAK,GAC9C,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,OAAO,YAAY,IAAI,CAAC,GAAG,GAAG;IAC/E;IACA,QAAQ,GAAG,EAAE;QACT,IAAI,OAAO,IAAI,CAAC,GAAG,EACf,IAAI,CAAC,IAAI;aACR;YACD,IAAI,CAAC,GAAG,IAAI;YACZ,IAAI,CAAC,GAAG,IAAI;QAChB;IACJ;IACA,SAAS,GAAG,EAAE;QACV,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,GACb,IAAI,CAAC,OAAO,CAAC;aACZ,IAAI,OAAO,IAAI,CAAC,GAAG,EACpB,IAAI,CAAC,IAAI;aACR;YACD,IAAI,CAAC,GAAG,IAAI;YACZ,IAAI,CAAC,GAAG,IAAI;QAChB;IACJ;AACJ;AAEA;;;;;AAKA,GACA,MAAM;IACF,YACA;;IAEA,GACA,IAAI,EACJ;;IAEA,GACA,EAAE,EAAE,KAAK,CAAE;QACP,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;;IAGA,GACA,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,sBAAsB,MAAK,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI;IAAE;IACtF;;;IAGA,GACA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,GAAG,sBAAsB,MAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE;IAAE;IACpF;;IAEA,GACA,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,EAAE;IAAE;IAC3C;;;;;IAKA,GACA,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,EAAE,yBAAyB,MAAK,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,wBAAwB,MAAK,IAAI;IAAG;IAC3H;;;IAGA,GACA,IAAI,YAAY;QACZ,IAAI,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE,2BAA2B;QACtD,OAAO,SAAS,IAAI,OAAO;IAC/B;IACA;;;;;IAKA,GACA,IAAI,aAAa;QACb,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE,8BAA8B;QAC1D,OAAO,SAAS,SAAS,0BAA0B,MAAK,YAAY;IACxE;IACA;;;IAGA,GACA,IAAI,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE;QACpB,IAAI,MAAM;QACV,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,OAAO,KAAK,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;QACzC,OACK;YACD,OAAO,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;YAChC,KAAK,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;QACjC;QACA,OAAO,QAAQ,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,IAAI,eAAe,MAAM,IAAI,IAAI,CAAC,KAAK;IAC9F;IACA;;IAEA,GACA,OAAO,IAAI,EAAE,KAAK,IAAI,EAAE;QACpB,IAAI,QAAQ,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,MAAM,EACxC,OAAO,gBAAgB,KAAK,CAAC,MAAM;QACvC,IAAI,OAAO,KAAK,GAAG,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,MAAM,IAAI,OAAO;QAC9E,OAAO,gBAAgB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE;IAC9C;IACA;;IAEA,GACA,GAAG,KAAK,EAAE,eAAe,KAAK,EAAE;QAC5B,OAAO,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,IAAI,CAAC,IAAI,IAAI,MAAM,IAAI,IACzD,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,MAAM,KAAK;IAClE;IACA;;IAEA,GACA,SAAS;QAAE,OAAO;YAAE,QAAQ,IAAI,CAAC,MAAM;YAAE,MAAM,IAAI,CAAC,IAAI;QAAC;IAAG;IAC5D;;;IAGA,GACA,OAAO,SAAS,IAAI,EAAE;QAClB,IAAI,CAAC,QAAQ,OAAO,KAAK,MAAM,IAAI,YAAY,OAAO,KAAK,IAAI,IAAI,UAC/D,MAAM,IAAI,WAAW;QACzB,OAAO,gBAAgB,KAAK,CAAC,KAAK,MAAM,EAAE,KAAK,IAAI;IACvD;IACA;;IAEA,GACA,OAAO,OAAO,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE;QAC3B,OAAO,IAAI,eAAe,MAAM,IAAI;IACxC;AACJ;AACA;;AAEA,GACA,MAAM;IACF,YACA;;;IAGA,GACA,MAAM,EACN;;;IAGA,GACA,SAAS,CAAE;QACP,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;IAGA,GACA,IAAI,MAAM,EAAE,QAAQ,CAAC,CAAC,EAAE;QACpB,IAAI,OAAO,KAAK,EACZ,OAAO,IAAI;QACf,OAAO,gBAAgB,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG,CAAC,QAAQ,SAAS,IAAI,CAAC,SAAS;IAC5F;IACA;;;;;IAKA,GACA,GAAG,KAAK,EAAE,eAAe,KAAK,EAAE;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,MAAM,MAAM,CAAC,MAAM,IACzC,IAAI,CAAC,SAAS,IAAI,MAAM,SAAS,EACjC,OAAO;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,IACpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,MAAM,CAAC,EAAE,EAAE,eACpC,OAAO;QACf,OAAO;IACX;IACA;;;;IAIA,GACA,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;IAAE;IACjD;;;IAGA,GACA,WAAW;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,IAAI,GAAG,IAAI,gBAAgB;YAAC,IAAI,CAAC,IAAI;SAAC,EAAE;IAC7E;IACA;;IAEA,GACA,SAAS,KAAK,EAAE,OAAO,IAAI,EAAE;QACzB,OAAO,gBAAgB,MAAM,CAAC;YAAC;SAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI,IAAI,CAAC,SAAS,GAAG;IAC3F;IACA;;;IAGA,GACA,aAAa,KAAK,EAAE,QAAQ,IAAI,CAAC,SAAS,EAAE;QACxC,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,KAAK;QAC9B,MAAM,CAAC,MAAM,GAAG;QAChB,OAAO,gBAAgB,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS;IACxD;IACA;;;IAGA,GACA,SAAS;QACL,OAAO;YAAE,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAAK,MAAM,IAAI,CAAC,SAAS;QAAC;IAC5E;IACA;;IAEA,GACA,OAAO,SAAS,IAAI,EAAE;QAClB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,MAAM,KAAK,OAAO,KAAK,IAAI,IAAI,YAAY,KAAK,IAAI,IAAI,KAAK,MAAM,CAAC,MAAM,EACvG,MAAM,IAAI,WAAW;QACzB,OAAO,IAAI,gBAAgB,KAAK,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,eAAe,QAAQ,CAAC,KAAK,KAAK,IAAI;IAC5F;IACA;;IAEA,GACA,OAAO,OAAO,MAAM,EAAE,OAAO,MAAM,EAAE;QACjC,OAAO,IAAI,gBAAgB;YAAC,gBAAgB,KAAK,CAAC,QAAQ;SAAM,EAAE;IACtE;IACA;;;IAGA,GACA,OAAO,OAAO,MAAM,EAAE,YAAY,CAAC,EAAE;QACjC,IAAI,OAAO,MAAM,IAAI,GACjB,MAAM,IAAI,WAAW;QACzB,IAAK,IAAI,MAAM,GAAG,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YAC7C,IAAI,QAAQ,MAAM,CAAC,EAAE;YACrB,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI,GAAG,KAC/C,OAAO,gBAAgB,UAAU,CAAC,OAAO,KAAK,IAAI;YACtD,MAAM,MAAM,EAAE;QAClB;QACA,OAAO,IAAI,gBAAgB,QAAQ;IACvC;IACA;;;IAGA,GACA,OAAO,OAAO,GAAG,EAAE,QAAQ,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE;QACjD,OAAO,eAAe,MAAM,CAAC,KAAK,KAAK,CAAC,SAAS,IAAI,IAAI,QAAQ,IAAI,EAAE,yBAAyB,MAAK,GAAG,wBAAwB,GAAE,IAC9H,CAAC,aAAa,OAAO,IAAI,KAAK,GAAG,CAAC,GAAG,UAAU,IAC9C,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,aAAa,SAAS,0BAA0B,GAAE,KAAK,EAAE,8BAA8B;IAChJ;IACA;;IAEA,GACA,OAAO,MAAM,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE;QAC9C,IAAI,QAAQ,AAAC,CAAC,eAAe,QAAQ,eAAe,KAAK,IAAI,aAAa,SAAS,0BAA0B,GAAE,KAAK,EAAE,8BAA8B,MAChJ,CAAC,aAAa,OAAO,IAAI,KAAK,GAAG,CAAC,GAAG,UAAU;QACnD,OAAO,OAAO,SAAS,eAAe,MAAM,CAAC,MAAM,QAAQ,GAAG,sBAAsB,MAAK,GAAG,wBAAwB,MAAK,SACnH,eAAe,MAAM,CAAC,QAAQ,MAAM,CAAC,OAAO,SAAS,EAAE,yBAAyB,MAAK,CAAC,IAAI;IACpG;IACA;;IAEA,GACA,OAAO,WAAW,MAAM,EAAE,YAAY,CAAC,EAAE;QACrC,IAAI,OAAO,MAAM,CAAC,UAAU;QAC5B,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,GAAG,EAAE,IAAI;QACrC,YAAY,OAAO,OAAO,CAAC;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACpC,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE,OAAO,MAAM,CAAC,IAAI,EAAE;YAC3C,IAAI,MAAM,KAAK,GAAG,MAAM,IAAI,IAAI,KAAK,EAAE,GAAG,MAAM,IAAI,GAAG,KAAK,EAAE,EAAE;gBAC5D,IAAI,OAAO,KAAK,IAAI,EAAE,KAAK,KAAK,GAAG,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE;gBACrD,IAAI,KAAK,WACL;gBACJ,OAAO,MAAM,CAAC,EAAE,GAAG,GAAG,MAAM,MAAM,GAAG,MAAM,IAAI,GAAG,gBAAgB,KAAK,CAAC,IAAI,QAAQ,gBAAgB,KAAK,CAAC,MAAM;YACpH;QACJ;QACA,OAAO,IAAI,gBAAgB,QAAQ;IACvC;AACJ;AACA,SAAS,eAAe,SAAS,EAAE,SAAS;IACxC,KAAK,IAAI,SAAS,UAAU,MAAM,CAC9B,IAAI,MAAM,EAAE,GAAG,WACX,MAAM,IAAI,WAAW;AACjC;AAEA,IAAI,SAAS;AACb;;;;;;;;;;;;AAYA,GACA,MAAM;IACF,YACA;;IAEA,GACA,OAAO,EACP;;IAEA,GACA,YAAY,EACZ;;IAEA,GACA,OAAO,EAAE,QAAQ,EAAE,OAAO,CAAE;QACxB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB;;QAEA,GACA,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,OAAO,GAAG,QAAQ,EAAE;QACzB,IAAI,CAAC,UAAU,GAAG,OAAO,WAAW,aAAa,QAAQ,IAAI,IAAI;IACrE;IACA;;;IAGA,GACA,IAAI,SAAS;QAAE,OAAO,IAAI;IAAE;IAC5B;;IAEA,GACA,OAAO,OAAO,SAAS,CAAC,CAAC,EAAE;QACvB,OAAO,IAAI,MAAM,OAAO,OAAO,IAAI,CAAC,CAAC,IAAM,CAAC,GAAG,OAAO,YAAY,IAAI,CAAC,CAAC,GAAG,IAAM,MAAM,CAAC,GAAG,OAAO,OAAO,IAAI,CAAC,CAAC,OAAO,OAAO,GAAG,YAAY,CAAC,GAAG,IAAM,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,MAAM,EAAE,OAAO,OAAO;IACnM;IACA;;IAEA,GACA,GAAG,KAAK,EAAE;QACN,OAAO,IAAI,cAAc,EAAE,EAAE,IAAI,EAAE,EAAE,mBAAmB,KAAI;IAChE;IACA;;;;;;;;IAQA,GACA,QAAQ,IAAI,EAAE,GAAG,EAAE;QACf,IAAI,IAAI,CAAC,QAAQ,EACb,MAAM,IAAI,MAAM;QACpB,OAAO,IAAI,cAAc,MAAM,IAAI,EAAE,EAAE,mBAAmB,KAAI;IAClE;IACA;;;IAGA,GACA,SAAS,IAAI,EAAE,GAAG,EAAE;QAChB,IAAI,IAAI,CAAC,QAAQ,EACb,MAAM,IAAI,MAAM;QACpB,OAAO,IAAI,cAAc,MAAM,IAAI,EAAE,EAAE,kBAAkB,KAAI;IACjE;IACA,KAAK,KAAK,EAAE,GAAG,EAAE;QACb,IAAI,CAAC,KACD,MAAM,CAAA,IAAK;QACf,OAAO,IAAI,CAAC,OAAO,CAAC;YAAC;SAAM,EAAE,CAAA,QAAS,IAAI,MAAM,KAAK,CAAC;IAC1D;AACJ;AACA,SAAS,UAAU,CAAC,EAAE,CAAC;IACnB,OAAO,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,CAAC,CAAC,GAAG,IAAM,MAAM,CAAC,CAAC,EAAE;AACzE;AACA,MAAM;IACF,YAAY,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAE;QAC1C,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,EAAE,GAAG;IACd;IACA,YAAY,SAAS,EAAE;QACnB,IAAI;QACJ,IAAI,SAAS,IAAI,CAAC,KAAK;QACvB,IAAI,UAAU,IAAI,CAAC,KAAK,CAAC,YAAY;QACrC,IAAI,KAAK,IAAI,CAAC,EAAE,EAAE,MAAM,SAAS,CAAC,GAAG,IAAI,GAAG,QAAQ,IAAI,CAAC,IAAI,IAAI,EAAE,kBAAkB;QACrF,IAAI,SAAS,OAAO,SAAS,OAAO,WAAW,EAAE;QACjD,KAAK,IAAI,OAAO,IAAI,CAAC,YAAY,CAAE;YAC/B,IAAI,OAAO,OACP,SAAS;iBACR,IAAI,OAAO,aACZ,SAAS;iBACR,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,GAC5E,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC;QACvC;QACA,OAAO;YACH,QAAO,KAAK;gBACR,MAAM,MAAM,CAAC,IAAI,GAAG,OAAO;gBAC3B,OAAO,EAAE,sBAAsB;YACnC;YACA,QAAO,KAAK,EAAE,EAAE;gBACZ,IAAI,AAAC,UAAU,GAAG,UAAU,IAAM,UAAU,CAAC,GAAG,UAAU,IAAI,GAAG,SAAS,KAAM,UAAU,OAAO,WAAW;oBACxG,IAAI,SAAS,OAAO;oBACpB,IAAI,QAAQ,CAAC,aAAa,QAAQ,MAAM,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,QAAQ,QAAQ,MAAM,MAAM,CAAC,IAAI,GAAG;wBACjG,MAAM,MAAM,CAAC,IAAI,GAAG;wBACpB,OAAO,EAAE,sBAAsB;oBACnC;gBACJ;gBACA,OAAO;YACX;YACA,aAAa,CAAC,OAAO;gBACjB,IAAI,QAAQ,UAAU,SAAS,MAAM,CAAC,OAAO,CAAC,GAAG;gBACjD,IAAI,WAAW,MAAM;oBACjB,IAAI,SAAS,QAAQ,UAAU;oBAC/B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAA;wBACxB,OAAO,eAAe,QAAQ,SAAS,KAAK,CAAC,SAAS,MAAM,KAAK,CAAC,OAC9D,eAAe,aAAa,SAAS,KAAK,CAAC,KAAK,UAAU,MAAM,KAAK,CAAC,KAAK,SAAS;oBAC5F,MAAM,CAAC,QAAQ,aAAa,SAAS,OAAO,QAAQ,QAAQ,WAAW,QAAQ,SAAS,OAAO,QAAQ,OAAO,GAAG;wBAC7G,MAAM,MAAM,CAAC,IAAI,GAAG;wBACpB,OAAO;oBACX;gBACJ,OACK;oBACD,SAAS,OAAO;gBACpB;gBACA,MAAM,MAAM,CAAC,IAAI,GAAG;gBACpB,OAAO,EAAE,sBAAsB;YACnC;QACJ;IACJ;AACJ;AACA,SAAS,aAAa,CAAC,EAAE,CAAC,EAAE,OAAO;IAC/B,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,EACpB,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAC1B,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,GACnB,OAAO;IACf,OAAO;AACX;AACA,SAAS,UAAU,KAAK,EAAE,KAAK;IAC3B,IAAI,UAAU;IACd,KAAK,IAAI,QAAQ,MACb,IAAI,WAAW,OAAO,QAAQ,EAAE,sBAAsB,KAClD,UAAU;IAClB,OAAO;AACX;AACA,SAAS,iBAAiB,SAAS,EAAE,KAAK,EAAE,SAAS;IACjD,IAAI,gBAAgB,UAAU,GAAG,CAAC,CAAA,IAAK,SAAS,CAAC,EAAE,EAAE,CAAC;IACtD,IAAI,gBAAgB,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;IAC7C,IAAI,UAAU,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,CAAC,IAAI,CAAC;IAC/C,IAAI,MAAM,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI;IACjC,SAAS,IAAI,KAAK;QACd,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC3C,IAAI,QAAQ,QAAQ,OAAO,aAAa,CAAC,EAAE;YAC3C,IAAI,aAAa,CAAC,EAAE,IAAI,EAAE,kBAAkB,KACxC,KAAK,IAAI,OAAO,MACZ,OAAO,IAAI,CAAC;iBAEhB,OAAO,IAAI,CAAC;QACpB;QACA,OAAO,MAAM,OAAO,CAAC;IACzB;IACA,OAAO;QACH,QAAO,KAAK;YACR,KAAK,IAAI,QAAQ,cACb,WAAW,OAAO;YACtB,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI;YACxB,OAAO,EAAE,sBAAsB;QACnC;QACA,QAAO,KAAK,EAAE,EAAE;YACZ,IAAI,CAAC,UAAU,OAAO,UAClB,OAAO;YACX,IAAI,QAAQ,IAAI;YAChB,IAAI,MAAM,OAAO,CAAC,OAAO,MAAM,MAAM,CAAC,IAAI,GACtC,OAAO;YACX,MAAM,MAAM,CAAC,IAAI,GAAG;YACpB,OAAO,EAAE,sBAAsB;QACnC;QACA,aAAY,KAAK,EAAE,QAAQ;YACvB,IAAI,aAAa,UAAU,OAAO;YAClC,IAAI,eAAe,SAAS,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,WAAW,SAAS,KAAK,CAAC;YAC/E,IAAI,gBAAgB,CAAC,cAAc,UAAU,WAAW,eAAe;gBACnE,MAAM,MAAM,CAAC,IAAI,GAAG;gBACpB,OAAO;YACX;YACA,IAAI,QAAQ,IAAI;YAChB,IAAI,MAAM,OAAO,CAAC,OAAO,WAAW;gBAChC,MAAM,MAAM,CAAC,IAAI,GAAG;gBACpB,OAAO;YACX;YACA,MAAM,MAAM,CAAC,IAAI,GAAG;YACpB,OAAO,EAAE,sBAAsB;QACnC;IACJ;AACJ;AACA,MAAM,YAAY,WAAW,GAAE,MAAM,MAAM,CAAC;IAAE,QAAQ;AAAK;AAC3D;;;AAGA,GACA,MAAM;IACF,YACA;;IAEA,GACA,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAC9B;;IAEA,GACA,IAAI,CAAE;QACF,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ;;QAEA,GACA,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;IAEA,GACA,OAAO,OAAO,MAAM,EAAE;QAClB,IAAI,QAAQ,IAAI,WAAW,UAAU,OAAO,MAAM,EAAE,OAAO,MAAM,EAAE,OAAO,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,MAAM,CAAC,GAAG;QAC1G,IAAI,OAAO,OAAO,EACd,MAAM,QAAQ,GAAG,OAAO,OAAO,CAAC;QACpC,OAAO;IACX;IACA,OAAO,KAAK,EAAE;QACV,IAAI,OAAO,MAAM,KAAK,CAAC,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,IAAI;QAC3D,OAAO,CAAC,CAAC,SAAS,QAAQ,SAAS,KAAK,IAAI,KAAK,IAAI,KAAK,MAAM,KAAK,IAAI,CAAC,OAAO,EAAE;IACvF;IACA;;IAEA,GACA,KAAK,SAAS,EAAE;QACZ,IAAI,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;QAChC,OAAO;YACH,QAAQ,CAAC;gBACL,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;gBAChC,OAAO,EAAE,sBAAsB;YACnC;YACA,QAAQ,CAAC,OAAO;gBACZ,IAAI,SAAS,MAAM,MAAM,CAAC,IAAI;gBAC9B,IAAI,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ;gBACjC,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,QACtB,OAAO;gBACX,MAAM,MAAM,CAAC,IAAI,GAAG;gBACpB,OAAO,EAAE,sBAAsB;YACnC;YACA,aAAa,CAAC,OAAO;gBACjB,IAAI,OAAO,MAAM,KAAK,CAAC,YAAY,UAAU,SAAS,KAAK,CAAC,YAAY;gBACxE,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,IAAI,CAAC,KAAK,UAAU,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,IAAI,IAAI,GAAG;oBAC5F,MAAM,MAAM,CAAC,IAAI,GAAG,OAAO,MAAM,CAAC;oBAClC,OAAO,EAAE,sBAAsB;gBACnC;gBACA,IAAI,SAAS,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM;oBAC1C,MAAM,MAAM,CAAC,IAAI,GAAG,SAAS,KAAK,CAAC,IAAI;oBACvC,OAAO;gBACX;gBACA,MAAM,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;gBAChC,OAAO,EAAE,sBAAsB;YACnC;QACJ;IACJ;IACA;;;;IAIA,GACA,KAAK,MAAM,EAAE;QACT,OAAO;YAAC,IAAI;YAAE,UAAU,EAAE,CAAC;gBAAE,OAAO,IAAI;gBAAE;YAAO;SAAG;IACxD;IACA;;;;IAIA,GACA,IAAI,YAAY;QAAE,OAAO,IAAI;IAAE;AACnC;AACA,MAAM,QAAQ;IAAE,QAAQ;IAAG,KAAK;IAAG,SAAS;IAAG,MAAM;IAAG,SAAS;AAAE;AACnE,SAAS,KAAK,KAAK;IACf,OAAO,CAAC,MAAQ,IAAI,cAAc,KAAK;AAC3C;AACA;;;;;;;;;AASA,GACA,MAAM,OAAO;IACT;;;IAGA,GACA,SAAS,WAAW,GAAE,KAAK,MAAM,OAAO;IACxC;;;IAGA,GACA,MAAM,WAAW,GAAE,KAAK,MAAM,IAAI;IAClC;;;IAGA,GACA,SAAS,WAAW,GAAE,KAAK,MAAM,OAAO;IACxC;;IAEA,GACA,KAAK,WAAW,GAAE,KAAK,MAAM,GAAG;IAChC;;;IAGA,GACA,QAAQ,WAAW,GAAE,KAAK,MAAM,MAAM;AAC1C;AACA,MAAM;IACF,YAAY,KAAK,EAAE,IAAI,CAAE;QACrB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;IAChB;AACJ;AACA;;;;;;AAMA,GACA,MAAM;IACF;;;IAGA,GACA,GAAG,GAAG,EAAE;QAAE,OAAO,IAAI,oBAAoB,IAAI,EAAE;IAAM;IACrD;;;IAGA,GACA,YAAY,OAAO,EAAE;QACjB,OAAO,YAAY,WAAW,CAAC,EAAE,CAAC;YAAE,aAAa,IAAI;YAAE,WAAW;QAAQ;IAC9E;IACA;;;IAGA,GACA,IAAI,KAAK,EAAE;QACP,OAAO,MAAM,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI;IAC7C;AACJ;AACA,MAAM;IACF,YAAY,WAAW,EAAE,KAAK,CAAE;QAC5B,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,KAAK,GAAG;IACjB;AACJ;AACA,MAAM;IACF,YAAY,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,CAAE;QACzE,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,cAAc,GAAG,EAAE;QACxB,MAAO,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,aAAa,MAAM,CACnD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,yBAAyB;IAC5D;IACA,YAAY,KAAK,EAAE;QACf,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACjC,OAAO,QAAQ,OAAO,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;IACtE;IACA,OAAO,QAAQ,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE;QACzC,IAAI,SAAS,EAAE;QACf,IAAI,SAAS,OAAO,MAAM,CAAC;QAC3B,IAAI,kBAAkB,IAAI;QAC1B,KAAK,IAAI,OAAO,QAAQ,MAAM,cAAc,iBAAkB;YAC1D,IAAI,eAAe,YACf,OAAO,IAAI,CAAC;iBAEZ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC;QACnE;QACA,IAAI,UAAU,OAAO,MAAM,CAAC;QAC5B,IAAI,eAAe,EAAE;QACrB,IAAI,eAAe,EAAE;QACrB,KAAK,IAAI,SAAS,OAAQ;YACtB,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,aAAa,MAAM,IAAI;YAC3C,aAAa,IAAI,CAAC,CAAA,IAAK,MAAM,IAAI,CAAC;QACtC;QACA,IAAI,YAAY,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,MAAM,CAAC,MAAM;QAC1F,IAAK,IAAI,MAAM,OAAQ;YACnB,IAAI,YAAY,MAAM,CAAC,GAAG,EAAE,QAAQ,SAAS,CAAC,EAAE,CAAC,KAAK;YACtD,IAAI,eAAe,aAAa,SAAS,CAAC,GAAG,IAAI,EAAE;YACnD,IAAI,UAAU,KAAK,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE,mBAAmB,MAAK;gBACzD,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,AAAC,aAAa,MAAM,IAAI,IAAK;gBACjD,IAAI,UAAU,cAAc,YAAY;oBACpC,aAAa,IAAI,CAAC,SAAS,KAAK,CAAC;gBACrC,OACK;oBACD,IAAI,QAAQ,MAAM,OAAO,CAAC,UAAU,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;oBACpD,aAAa,IAAI,CAAC,YAAY,MAAM,OAAO,CAAC,OAAO,SAAS,KAAK,CAAC,UAAU,SAAS,KAAK,CAAC,SAAS;gBACxG;YACJ,OACK;gBACD,KAAK,IAAI,KAAK,UAAW;oBACrB,IAAI,EAAE,IAAI,IAAI,EAAE,mBAAmB,KAAI;wBACnC,OAAO,CAAC,EAAE,EAAE,CAAC,GAAG,AAAC,aAAa,MAAM,IAAI,IAAK;wBAC7C,aAAa,IAAI,CAAC,EAAE,KAAK;oBAC7B,OACK;wBACD,OAAO,CAAC,EAAE,EAAE,CAAC,GAAG,aAAa,MAAM,IAAI;wBACvC,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,WAAW,CAAC;oBACzC;gBACJ;gBACA,OAAO,CAAC,MAAM,EAAE,CAAC,GAAG,aAAa,MAAM,IAAI;gBAC3C,aAAa,IAAI,CAAC,CAAA,IAAK,iBAAiB,GAAG,OAAO;YACtD;QACJ;QACA,IAAI,UAAU,aAAa,GAAG,CAAC,CAAA,IAAK,EAAE;QACtC,OAAO,IAAI,cAAc,MAAM,iBAAiB,SAAS,SAAS,cAAc;IACpF;AACJ;AACA,SAAS,QAAQ,SAAS,EAAE,YAAY,EAAE,eAAe;IACrD,IAAI,SAAS;QAAC,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;QAAE,EAAE;KAAC;IACjC,IAAI,OAAO,IAAI;IACf,SAAS,MAAM,GAAG,EAAE,IAAI;QACpB,IAAI,QAAQ,KAAK,GAAG,CAAC;QACrB,IAAI,SAAS,MAAM;YACf,IAAI,SAAS,MACT;YACJ,IAAI,QAAQ,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC;YAClC,IAAI,QAAQ,CAAC,GACT,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO;YAChC,IAAI,eAAe,qBACf,gBAAgB,MAAM,CAAC,IAAI,WAAW;QAC9C;QACA,KAAK,GAAG,CAAC,KAAK;QACd,IAAI,MAAM,OAAO,CAAC,MAAM;YACpB,KAAK,IAAI,KAAK,IACV,MAAM,GAAG;QACjB,OACK,IAAI,eAAe,qBAAqB;YACzC,IAAI,gBAAgB,GAAG,CAAC,IAAI,WAAW,GACnC,MAAM,IAAI,WAAW,CAAC,0CAA0C,CAAC;YACrE,IAAI,UAAU,aAAa,GAAG,CAAC,IAAI,WAAW,KAAK,IAAI,KAAK;YAC5D,gBAAgB,GAAG,CAAC,IAAI,WAAW,EAAE;YACrC,MAAM,SAAS;QACnB,OACK,IAAI,eAAe,eAAe;YACnC,MAAM,IAAI,KAAK,EAAE,IAAI,IAAI;QAC7B,OACK,IAAI,eAAe,YAAY;YAChC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;YAClB,IAAI,IAAI,QAAQ,EACZ,MAAM,IAAI,QAAQ,EAAE;QAC5B,OACK,IAAI,eAAe,eAAe;YACnC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;YAClB,IAAI,IAAI,KAAK,CAAC,UAAU,EACpB,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE,MAAM,OAAO;QACjD,OACK;YACD,IAAI,UAAU,IAAI,SAAS;YAC3B,IAAI,CAAC,SACD,MAAM,IAAI,MAAM,CAAC,+CAA+C,EAAE,IAAI,iHAAiH,CAAC;YAC5L,MAAM,SAAS;QACnB;IACJ;IACA,MAAM,WAAW,MAAM,OAAO;IAC9B,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,CAAC;AAC5C;AACA,SAAS,WAAW,KAAK,EAAE,IAAI;IAC3B,IAAI,OAAO,GACP,OAAO,EAAE,uBAAuB;IACpC,IAAI,MAAM,QAAQ;IAClB,IAAI,SAAS,MAAM,MAAM,CAAC,IAAI;IAC9B,IAAI,UAAU,EAAE,wBAAwB,KACpC,MAAM,IAAI,MAAM;IACpB,IAAI,SAAS,EAAE,uBAAuB,KAClC,OAAO;IACX,MAAM,MAAM,CAAC,IAAI,GAAG,EAAE,wBAAwB;IAC9C,IAAI,UAAU,MAAM,WAAW,CAAC,OAAO,MAAM,MAAM,CAAC,YAAY,CAAC,IAAI;IACrE,OAAO,MAAM,MAAM,CAAC,IAAI,GAAG,EAAE,uBAAuB,MAAK;AAC7D;AACA,SAAS,QAAQ,KAAK,EAAE,IAAI;IACxB,OAAO,OAAO,IAAI,MAAM,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,GAAG,MAAM,MAAM,CAAC,QAAQ,EAAE;AACpF;AAEA,MAAM,eAAe,WAAW,GAAE,MAAM,MAAM;AAC9C,MAAM,0BAA0B,WAAW,GAAE,MAAM,MAAM,CAAC;IACtD,SAAS,CAAA,SAAU,OAAO,IAAI,CAAC,CAAA,IAAK;IACpC,QAAQ;AACZ;AACA,MAAM,gBAAgB,WAAW,GAAE,MAAM,MAAM,CAAC;IAC5C,SAAS,CAAA,SAAU,OAAO,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG;IAC/C,QAAQ;AACZ;AACA,MAAM,eAAe,WAAW,GAAE,MAAM,MAAM;AAC9C,MAAM,oBAAoB,WAAW,GAAE,MAAM,MAAM;AACnD,MAAM,sBAAsB,WAAW,GAAE,MAAM,MAAM;AACrD,MAAM,WAAW,WAAW,GAAE,MAAM,MAAM,CAAC;IACvC,SAAS,CAAA,SAAU,OAAO,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG;AACnD;AAEA;;;;;;;;AAQA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,IAAI,EACJ;;IAEA,GACA,KAAK,CAAE;QACH,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;IAEA,GACA,OAAO,SAAS;QAAE,OAAO,IAAI;IAAkB;AACnD;AACA;;AAEA,GACA,MAAM;IACF;;IAEA,GACA,GAAG,KAAK,EAAE;QAAE,OAAO,IAAI,WAAW,IAAI,EAAE;IAAQ;AACpD;AACA;;;AAGA,GACA,MAAM;IACF;;IAEA,GACA,YACA,4DAA4D;IAC5D,0DAA0D;IAC1D,kEAAkE;IAClE,qBAAqB;IACrB;;IAEA,GACA,GAAG,CAAE;QACD,IAAI,CAAC,GAAG,GAAG;IACf;IACA;;;IAGA,GACA,GAAG,KAAK,EAAE;QAAE,OAAO,IAAI,YAAY,IAAI,EAAE;IAAQ;AACrD;AACA;;;;;;AAMA,GACA,MAAM;IACF;;IAEA,GACA,YACA;;IAEA,GACA,IAAI,EACJ;;IAEA,GACA,KAAK,CAAE;QACH,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;;IAGA,GACA,IAAI,OAAO,EAAE;QACT,IAAI,SAAS,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE;QACvC,OAAO,WAAW,YAAY,YAAY,UAAU,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,IAAI,YAAY,IAAI,CAAC,IAAI,EAAE;IACvG;IACA;;;IAGA,GACA,GAAG,IAAI,EAAE;QAAE,OAAO,IAAI,CAAC,IAAI,IAAI;IAAM;IACrC;;;;;;IAMA,GACA,OAAO,OAAO,OAAO,CAAC,CAAC,EAAE;QACrB,OAAO,IAAI,gBAAgB,KAAK,GAAG,IAAI,CAAC,CAAA,IAAK,CAAC;IAClD;IACA;;IAEA,GACA,OAAO,WAAW,OAAO,EAAE,OAAO,EAAE;QAChC,IAAI,CAAC,QAAQ,MAAM,EACf,OAAO;QACX,IAAI,SAAS,EAAE;QACf,KAAK,IAAI,UAAU,QAAS;YACxB,IAAI,SAAS,OAAO,GAAG,CAAC;YACxB,IAAI,QACA,OAAO,IAAI,CAAC;QACpB;QACA,OAAO;IACX;AACJ;AACA;;;;;;AAMA,GACA,YAAY,WAAW,GAAG,WAAW,GAAE,YAAY,MAAM;AACzD;;AAEA,GACA,YAAY,YAAY,GAAG,WAAW,GAAE,YAAY,MAAM;AAC1D;;;;;;;;AAQA,GACA,MAAM;IACF,YACA;;IAEA,GACA,UAAU,EACV;;IAEA,GACA,OAAO,EACP;;;IAGA,GACA,SAAS,EACT;;IAEA,GACA,OAAO,EACP;;IAEA,GACA,WAAW,EACX;;;IAGA,GACA,cAAc,CAAE;QACZ,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,cAAc,GAAG;QACtB;;QAEA,GACA,IAAI,CAAC,IAAI,GAAG;QACZ;;QAEA,GACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,WACA,eAAe,WAAW,QAAQ,SAAS;QAC/C,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,IAAI,YAAY,IAAI,GACnD,IAAI,CAAC,WAAW,GAAG,YAAY,MAAM,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC,KAAK,GAAG;IAC1E;IACA;;IAEA,GACA,OAAO,OAAO,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc,EAAE;QAChF,OAAO,IAAI,YAAY,YAAY,SAAS,WAAW,SAAS,aAAa;IACjF;IACA;;;;;;;IAOA,GACA,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;IAC5E;IACA;;;;;IAKA,GACA,IAAI,eAAe;QACf,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO;IACvE;IACA;;;;;IAKA,GACA,IAAI,QAAQ;QACR,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI;QACzC,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;;IAEA,GACA,WAAW,IAAI,EAAE;QACb,KAAK,IAAI,OAAO,IAAI,CAAC,WAAW,CAC5B,IAAI,IAAI,IAAI,IAAI,MACZ,OAAO,IAAI,KAAK;QACxB,OAAO;IACX;IACA;;IAEA,GACA,IAAI,aAAa;QAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;IAAE;IAC/C;;;;;IAKA,GACA,IAAI,eAAe;QAAE,OAAO,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;IAAE;IACzE;;;;;;IAMA,GACA,YAAY,KAAK,EAAE;QACf,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,SAAS;QAC7C,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE,MAAM,GAAG,MAAM,MAAM,IAAI,EAAE,KAAK,CAAC,GAAG,MAAM,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,MAAM,CAAC,IAAI,GAAG,CAAC;IACzH;AACJ;AACA;;;AAGA,GACA,YAAY,IAAI,GAAG,WAAW,GAAE,WAAW,MAAM;AACjD;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,GACA,YAAY,SAAS,GAAG,WAAW,GAAE,WAAW,MAAM;AACtD;;;AAGA,GACA,YAAY,YAAY,GAAG,WAAW,GAAE,WAAW,MAAM;AACzD;;;;;AAKA,GACA,YAAY,MAAM,GAAG,WAAW,GAAE,WAAW,MAAM;AACnD,SAAS,WAAW,CAAC,EAAE,CAAC;IACpB,IAAI,SAAS,EAAE;IACf,IAAK,IAAI,KAAK,GAAG,KAAK,IAAK;QACvB,IAAI,MAAM;QACV,IAAI,KAAK,EAAE,MAAM,IAAI,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG;YACrD,OAAO,CAAC,CAAC,KAAK;YACd,KAAK,CAAC,CAAC,KAAK;QAChB,OACK,IAAI,KAAK,EAAE,MAAM,EAAE;YACpB,OAAO,CAAC,CAAC,KAAK;YACd,KAAK,CAAC,CAAC,KAAK;QAChB,OAEI,OAAO;QACX,IAAI,CAAC,OAAO,MAAM,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,GAAG,MAC9C,OAAO,IAAI,CAAC,MAAM;aACjB,IAAI,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,GAAG,IACjC,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,GAAG;IACpC;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,CAAC,EAAE,UAAU;IACtC,IAAI;IACJ,IAAI,SAAS,SAAS;IACtB,IAAI,YAAY;QACZ,UAAU,EAAE,OAAO;QACnB,UAAU,UAAU,KAAK,CAAC,EAAE,OAAO,CAAC,MAAM;QAC1C,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO;IACzC,OACK;QACD,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO;QACjC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE;QACvC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC;IAChC;IACA,OAAO;QACH;QACA,WAAW,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC;QACnH,SAAS,YAAY,UAAU,CAAC,EAAE,OAAO,EAAE,SAAS,MAAM,CAAC,YAAY,UAAU,CAAC,EAAE,OAAO,EAAE;QAC7F,aAAa,EAAE,WAAW,CAAC,MAAM,GAAG,EAAE,WAAW,CAAC,MAAM,CAAC,EAAE,WAAW,IAAI,EAAE,WAAW;QACvF,gBAAgB,EAAE,cAAc,IAAI,EAAE,cAAc;IACxD;AACJ;AACA,SAAS,wBAAwB,KAAK,EAAE,IAAI,EAAE,OAAO;IACjD,IAAI,MAAM,KAAK,SAAS,EAAE,cAAc,QAAQ,KAAK,WAAW;IAChE,IAAI,KAAK,SAAS,EACd,cAAc,YAAY,MAAM,CAAC,YAAY,SAAS,CAAC,EAAE,CAAC,KAAK,SAAS;IAC5E,OAAO;QACH,SAAS,KAAK,OAAO,YAAY,YAAY,KAAK,OAAO,GACnD,UAAU,EAAE,CAAC,KAAK,OAAO,IAAI,EAAE,EAAE,SAAS,MAAM,KAAK,CAAC;QAC5D,WAAW,OAAO,CAAC,eAAe,kBAAkB,MAAM,gBAAgB,MAAM,CAAC,IAAI,MAAM,EAAE,IAAI,IAAI,CAAC;QACtG,SAAS,QAAQ,KAAK,OAAO;QAC7B;QACA,gBAAgB,CAAC,CAAC,KAAK,cAAc;IACzC;AACJ;AACA,SAAS,mBAAmB,KAAK,EAAE,KAAK,EAAE,MAAM;IAC5C,IAAI,IAAI,wBAAwB,OAAO,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,MAAM;IACrF,IAAI,MAAM,MAAM,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,OACpC,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,OACpB,SAAS;QACb,IAAI,MAAM,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,UAAU;QAC/B,IAAI,iBAAiB,GAAG,wBAAwB,OAAO,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,SAAS,GAAG,MAAM,GAAG,CAAC,MAAM,GAAG;IACpH;IACA,IAAI,KAAK,YAAY,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,WAAW,EAAE,EAAE,cAAc;IACrG,OAAO,kBAAkB,SAAS,kBAAkB,MAAM;AAC9D;AACA,yDAAyD;AACzD,SAAS,kBAAkB,EAAE;IACzB,IAAI,QAAQ,GAAG,UAAU;IACzB,iBAAiB;IACjB,IAAI,SAAS;IACb,KAAK,IAAI,UAAU,MAAM,KAAK,CAAC,cAAe;QAC1C,IAAI,QAAQ,OAAO;QACnB,IAAI,UAAU,OAAO;YACjB,SAAS;YACT;QACJ;QACA,IAAI,MAAM,OAAO,CAAC,QACd,SAAS,WAAW,OAAO,QAAQ,WAAW,QAAQ;IAC9D;IACA,IAAI,WAAW,MAAM;QACjB,IAAI,SAAS;QACb,IAAI,WAAW,OAAO;YAClB,OAAO,GAAG,OAAO,CAAC,YAAY;YAC9B,UAAU,UAAU,KAAK,CAAC,MAAM,GAAG,CAAC,MAAM;QAC9C,OACK;YACD,IAAI,WAAW,GAAG,OAAO,CAAC,MAAM,CAAC;YACjC,UAAU,SAAS,OAAO;YAC1B,OAAO,SAAS,QAAQ,CAAC,OAAO,CAAC,SAAS,OAAO,EAAE,YAAY;QACnE;QACA,KAAK,YAAY,MAAM,CAAC,OAAO,SAAS,GAAG,SAAS,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,YAAY,UAAU,CAAC,GAAG,OAAO,EAAE,OAAO,GAAG,WAAW,EAAE,GAAG,cAAc;IAC/J;IACA,sBAAsB;IACtB,IAAI,UAAU,MAAM,KAAK,CAAC;IAC1B,IAAK,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC1C,IAAI,WAAW,OAAO,CAAC,EAAE,CAAC;QAC1B,IAAI,oBAAoB,aACpB,KAAK;aACJ,IAAI,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,IAAI,KAAK,QAAQ,CAAC,EAAE,YAAY,aAC/E,KAAK,QAAQ,CAAC,EAAE;aAEhB,KAAK,mBAAmB,OAAO,QAAQ,WAAW;IAC1D;IACA,OAAO;AACX;AACA,SAAS,kBAAkB,EAAE;IACzB,IAAI,QAAQ,GAAG,UAAU,EAAE,YAAY,MAAM,KAAK,CAAC,sBAAsB,OAAO;IAChF,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC5C,IAAI,YAAY,SAAS,CAAC,EAAE,CAAC;QAC7B,IAAI,aAAa,OAAO,IAAI,CAAC,WAAW,MAAM,EAC1C,OAAO,iBAAiB,MAAM,wBAAwB,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS,GAAG;IACvG;IACA,OAAO,QAAQ,KAAK,KAAK,YAAY,MAAM,CAAC,OAAO,GAAG,OAAO,EAAE,GAAG,SAAS,EAAE,KAAK,OAAO,EAAE,KAAK,WAAW,EAAE,KAAK,cAAc;AACpI;AACA,MAAM,OAAO,EAAE;AACf,SAAS,QAAQ,KAAK;IAClB,OAAO,SAAS,OAAO,OAAO,MAAM,OAAO,CAAC,SAAS,QAAQ;QAAC;KAAM;AACxE;AAEA;;;;AAIA,GACA,IAAI,eAAe,WAAW,GAAE,AAAC,SAAU,YAAY;IACnD;;IAEA,GACA,YAAY,CAAC,YAAY,CAAC,OAAO,GAAG,EAAE,GAAG;IACzC;;IAEA,GACA,YAAY,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC1C;;IAEA,GACA,YAAY,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE,GAAG;IAC9C,OAAO;AAAY,EAAG,gBAAgB,CAAC,eAAe,CAAC,CAAC;AACxD,MAAM,6BAA6B;AACnC,IAAI;AACJ,IAAI;IACA,WAAW,WAAW,GAAE,IAAI,OAAO,iCAAiC;AACxE,EACA,OAAO,GAAG,CAAE;AACZ,SAAS,YAAY,GAAG;IACpB,IAAI,UACA,OAAO,SAAS,IAAI,CAAC;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QACjC,IAAI,KAAK,GAAG,CAAC,EAAE;QACf,IAAI,KAAK,IAAI,CAAC,OAAO,KAAK,UAAU,CAAC,GAAG,WAAW,MAAM,GAAG,WAAW,MAAM,2BAA2B,IAAI,CAAC,GAAG,GAC5G,OAAO;IACf;IACA,OAAO;AACX;AACA,SAAS,gBAAgB,SAAS;IAC9B,OAAO,CAAC;QACJ,IAAI,CAAC,KAAK,IAAI,CAAC,OACX,OAAO,aAAa,KAAK;QAC7B,IAAI,YAAY,OACZ,OAAO,aAAa,IAAI;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAClC,IAAI,KAAK,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,GAC9B,OAAO,aAAa,IAAI;QAChC,OAAO,aAAa,KAAK;IAC7B;AACJ;AAEA;;;;;;;;AAQA,GACA,MAAM;IACF,YACA;;IAEA,GACA,MAAM,EACN;;IAEA,GACA,GAAG,EACH;;IAEA,GACA,SAAS,EACT;;IAEA,GACA,MAAM,EAAE,WAAW,EAAE,EAAE,CAAE;QACrB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG,OAAO,cAAc,CAAC,KAAK;QACzC,IAAI,CAAC,WAAW,GAAG;QACnB,kEAAkE;QAClE,kDAAkD;QAClD,IAAI,IACA,GAAG,MAAM,GAAG,IAAI;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,IACjD,WAAW,IAAI,EAAE,KAAK;QAC1B,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,MAAM,KAAK,EAAE,UAAU,IAAI,EAAE;QACzB,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACxC,IAAI,QAAQ,MAAM;YACd,IAAI,SACA,MAAM,IAAI,WAAW;YACzB,OAAO;QACX;QACA,WAAW,IAAI,EAAE;QACjB,OAAO,QAAQ,IAAI,EAAE;IACzB;IACA;;;;;;;;;;;;;;IAcA,GACA,OAAO,GAAG,KAAK,EAAE;QACb,OAAO,mBAAmB,IAAI,EAAE,OAAO;IAC3C;IACA;;IAEA,GACA,iBAAiB,EAAE,EAAE;QACjB,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG;QACjD,KAAK,IAAI,UAAU,GAAG,OAAO,CAAE;YAC3B,IAAI,OAAO,EAAE,CAAC,YAAY,WAAW,GAAG;gBACpC,IAAI,MAAM;oBACN,eAAe,IAAI;oBACnB,KAAK,YAAY,CAAC,OAAO,CAAC,CAAC,KAAK,MAAQ,aAAa,GAAG,CAAC,KAAK;oBAC9D,OAAO;gBACX;gBACA,aAAa,GAAG,CAAC,OAAO,KAAK,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,SAAS;YACrE,OACK,IAAI,OAAO,EAAE,CAAC,YAAY,WAAW,GAAG;gBACzC,OAAO;gBACP,OAAO,OAAO,KAAK;YACvB,OACK,IAAI,OAAO,EAAE,CAAC,YAAY,YAAY,GAAG;gBAC1C,OAAO;gBACP,OAAO,QAAQ,MAAM,MAAM,CAAC,OAAO,KAAK;YAC5C;QACJ;QACA,IAAI;QACJ,IAAI,CAAC,MAAM;YACP,OAAO,cAAc,OAAO,CAAC,MAAM,cAAc,IAAI;YACrD,IAAI,oBAAoB,IAAI,YAAY,MAAM,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,GAAG,CAAC,IAAM,OAAO,CAAC,OAAO,OAAS,KAAK,WAAW,CAAC,OAAO,IAAI,GAAG;YAC3J,cAAc,kBAAkB,MAAM;QAC1C,OACK;YACD,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK;QAC5C;QACA,IAAI,YAAY,GAAG,UAAU,CAAC,KAAK,CAAC,2BAA2B,GAAG,YAAY,GAAG,GAAG,YAAY,CAAC,QAAQ;QACzG,IAAI,YAAY,MAAM,GAAG,MAAM,EAAE,WAAW,aAAa,CAAC,OAAO,OAAS,KAAK,MAAM,CAAC,OAAO,KAAK;IACtG;IACA;;;IAGA,GACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,OAAO,QAAQ,UACf,OAAO,IAAI,CAAC,MAAM,CAAC;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,CAAA,QAAS,CAAC;gBAAE,SAAS;oBAAE,MAAM,MAAM,IAAI;oBAAE,IAAI,MAAM,EAAE;oBAAE,QAAQ;gBAAK;gBAC1F,OAAO,gBAAgB,MAAM,CAAC,MAAM,IAAI,GAAG,KAAK,MAAM;YAAE,CAAC;IACjE;IACA;;;;;;;;;;IAUA,GACA,cAAc,CAAC,EAAE;QACb,IAAI,MAAM,IAAI,CAAC,SAAS;QACxB,IAAI,UAAU,EAAE,IAAI,MAAM,CAAC,EAAE;QAC7B,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,QAAQ,OAAO,GAAG,SAAS;YAAC,QAAQ,KAAK;SAAC;QACrE,IAAI,UAAU,QAAQ,QAAQ,OAAO;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,SAAS,EAAE,IAAI,MAAM,CAAC,EAAE;YAC5B,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,OAAO,OAAO,GAAG,YAAY,WAAW,GAAG,CAAC;YAC1E,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IACnB,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC;YAC9B,IAAI,QAAQ,QAAQ,OAAO,CAAC,YAAY;YACxC,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,GAAG,CAAC;YAC7B,UAAU,QAAQ,OAAO,CAAC;YAC1B,UAAU,YAAY,UAAU,CAAC,SAAS,WAAW,MAAM,CAAC,YAAY,UAAU,CAAC,QAAQ,OAAO,OAAO,GAAG;QAChH;QACA,OAAO;YACH;YACA,WAAW,gBAAgB,MAAM,CAAC,QAAQ,IAAI,SAAS;YACvD;QACJ;IACJ;IACA;;;;IAIA,GACA,QAAQ,OAAO,EAAE,EAAE;QACf,IAAI,gBAAgB,WAChB,OAAO;QACX,OAAO,UAAU,EAAE,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,aAAa;IACnF;IACA;;;;IAIA,GACA,OAAO,MAAM,EAAE;QACX,OAAO,KAAK,EAAE,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,aAAa,KAAK;IACzE;IACA;;IAEA,GACA,SAAS,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE;QACrC,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS;IACxD;IACA;;IAEA,GACA,MAAM,KAAK,EAAE;QACT,IAAI,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACxC,IAAI,QAAQ,MACR,OAAO,MAAM,OAAO;QACxB,WAAW,IAAI,EAAE;QACjB,OAAO,QAAQ,IAAI,EAAE;IACzB;IACA;;;;;IAKA,GACA,OAAO,MAAM,EAAE;QACX,IAAI,SAAS;YACT,KAAK,IAAI,CAAC,QAAQ;YAClB,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM;QACpC;QACA,IAAI,QACA,IAAK,IAAI,QAAQ,OAAQ;YACrB,IAAI,QAAQ,MAAM,CAAC,KAAK;YACxB,IAAI,iBAAiB,cAAc,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,IAAI,MAChE,MAAM,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI;QACvE;QACJ,OAAO;IACX;IACA;;;;;IAKA,GACA,OAAO,SAAS,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE;QACvC,IAAI,CAAC,QAAQ,OAAO,KAAK,GAAG,IAAI,UAC5B,MAAM,IAAI,WAAW;QACzB,IAAI,YAAY,EAAE;QAClB,IAAI,QACA,IAAK,IAAI,QAAQ,OAAQ;YACrB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,OAAO;gBAClD,IAAI,QAAQ,MAAM,CAAC,KAAK,EAAE,QAAQ,IAAI,CAAC,KAAK;gBAC5C,UAAU,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA,QAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO;YAClE;QACJ;QACJ,OAAO,YAAY,MAAM,CAAC;YACtB,KAAK,KAAK,GAAG;YACb,WAAW,gBAAgB,QAAQ,CAAC,KAAK,SAAS;YAClD,YAAY,OAAO,UAAU,GAAG,UAAU,MAAM,CAAC;gBAAC,OAAO,UAAU;aAAC,IAAI;QAC5E;IACJ;IACA;;;;IAIA,GACA,OAAO,OAAO,SAAS,CAAC,CAAC,EAAE;QACvB,IAAI,gBAAgB,cAAc,OAAO,CAAC,OAAO,UAAU,IAAI,EAAE,EAAE,IAAI;QACvE,IAAI,MAAM,OAAO,GAAG,YAAY,OAAO,OAAO,GAAG,GAC3C,KAAK,EAAE,CAAC,CAAC,OAAO,GAAG,IAAI,EAAE,EAAE,KAAK,CAAC,cAAc,WAAW,CAAC,YAAY,aAAa,KAAK;QAC/F,IAAI,YAAY,CAAC,OAAO,SAAS,GAAG,gBAAgB,MAAM,CAAC,KACrD,OAAO,SAAS,YAAY,kBAAkB,OAAO,SAAS,GAC1D,gBAAgB,MAAM,CAAC,OAAO,SAAS,CAAC,MAAM,EAAE,OAAO,SAAS,CAAC,IAAI;QAC/E,eAAe,WAAW,IAAI,MAAM;QACpC,IAAI,CAAC,cAAc,WAAW,CAAC,0BAC3B,YAAY,UAAU,QAAQ;QAClC,OAAO,IAAI,YAAY,eAAe,KAAK,WAAW,cAAc,YAAY,CAAC,GAAG,CAAC,IAAM,OAAO,CAAC,OAAO,OAAS,KAAK,MAAM,CAAC,QAAQ;IAC3I;IACA;;;IAGA,GACA,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,OAAO;IAAG;IACxD;;;IAGA,GACA,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,aAAa,KAAK;IAAM;IACxE;;;IAGA,GACA,IAAI,WAAW;QAAE,OAAO,IAAI,CAAC,KAAK,CAAC;IAAW;IAC9C;;;;;;;;;IASA,GACA,OAAO,MAAM,EAAE,GAAG,MAAM,EAAE;QACtB,KAAK,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,OAAO,EAC1C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,SAAS;YACnD,SAAS,GAAG,CAAC,OAAO;YACpB;QACJ;QACJ,IAAI,OAAO,MAAM,EACb,SAAS,OAAO,OAAO,CAAC,eAAe,CAAC,GAAG;YACvC,IAAI,KAAK,KACL,OAAO;YACX,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC;YAChB,OAAO,CAAC,KAAK,IAAI,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE;QACtD;QACJ,OAAO;IACX;IACA;;;;;;;;;;;;;;;IAeA,GACA,eAAe,IAAI,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,EAAE;QACjC,IAAI,SAAS,EAAE;QACf,KAAK,IAAI,YAAY,IAAI,CAAC,KAAK,CAAC,cAAe;YAC3C,KAAK,IAAI,UAAU,SAAS,IAAI,EAAE,KAAK,MAAO;gBAC1C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,OAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YAChC;QACJ;QACA,OAAO;IACX;IACA;;;;;;;;;;IAUA,GACA,gBAAgB,EAAE,EAAE;QAChB,OAAO,gBAAgB,IAAI,CAAC,cAAc,CAAC,aAAa,IAAI,IAAI,CAAC;IACrE;IACA;;;;;IAKA,GACA,OAAO,GAAG,EAAE;QACR,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;QAC7C,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC;QAC/B,IAAI,QAAQ,MAAM,MAAM,MAAM,MAAM;QACpC,MAAO,QAAQ,EAAG;YACd,IAAI,OAAO,iBAAiB,MAAM,OAAO;YACzC,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,WAAW,aAAa,IAAI,EACjD;YACJ,QAAQ;QACZ;QACA,MAAO,MAAM,OAAQ;YACjB,IAAI,OAAO,iBAAiB,MAAM;YAClC,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,UAAU,aAAa,IAAI,EAC/C;YACJ,MAAM;QACV;QACA,OAAO,SAAS,MAAM,OAAO,gBAAgB,KAAK,CAAC,QAAQ,MAAM,MAAM;IAC3E;AACJ;AACA;;;;;;;AAOA,GACA,YAAY,uBAAuB,GAAG;AACtC;;;;AAIA,GACA,YAAY,OAAO,GAAG,WAAW,GAAE,MAAM,MAAM,CAAC;IAC5C,SAAS,CAAA,SAAU,OAAO,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG;AACnD;AACA;;;;;;;;AAQA,GACA,YAAY,aAAa,GAAG;AAC5B;;;;;;;;;;;;AAYA,GACA,YAAY,QAAQ,GAAG;AACvB;;;;;AAKA,GACA,YAAY,OAAO,GAAG,WAAW,GAAE,MAAM,MAAM,CAAC;IAC5C,SAAQ,CAAC,EAAE,CAAC;QACR,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,CAAC;QAC1C,OAAO,GAAG,MAAM,IAAI,GAAG,MAAM,IAAI,GAAG,KAAK,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;IAC/D;AACJ;AACA;;;AAGA,GACA,YAAY,YAAY,GAAG;AAC3B;;;;;;;;;;;;;AAaA,GACA,YAAY,YAAY,GAAG;AAC3B;;;;;;;;;;;;;;;;;;AAkBA,GACA,YAAY,iBAAiB,GAAG;AAChC;;;;;;;;;;;;AAYA,GACA,YAAY,mBAAmB,GAAG;AAClC,YAAY,WAAW,GAAG,WAAW,GAAE,YAAY,MAAM;AAEzD;;;;;;;;AAQA,GACA,SAAS,cAAc,OAAO,EAAE,QAAQ,EACxC,UAAU,CAAC,CAAC;IACR,IAAI,SAAS,CAAC;IACd,KAAK,IAAI,UAAU,QACf,KAAK,IAAI,OAAO,OAAO,IAAI,CAAC,QAAS;QACjC,IAAI,QAAQ,MAAM,CAAC,IAAI,EAAE,UAAU,MAAM,CAAC,IAAI;QAC9C,IAAI,YAAY,WACZ,MAAM,CAAC,IAAI,GAAG;aACb,IAAI,YAAY,SAAS,UAAU,aAAa,cAAc;aAC9D,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,MACzC,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,SAAS;aAEpC,MAAM,IAAI,MAAM,qCAAqC;IAC7D;IACJ,IAAK,IAAI,OAAO,SACZ,IAAI,MAAM,CAAC,IAAI,KAAK,WAChB,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;IACnC,OAAO;AACX;AAEA;;;AAGA,GACA,MAAM;IACF;;;;;;IAMA,GACA,GAAG,KAAK,EAAE;QAAE,OAAO,IAAI,IAAI;IAAO;IAClC;;IAEA,GACA,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE;QAAE,OAAO,MAAM,MAAM,CAAC,MAAM,IAAI,IAAI;IAAG;AAClE;AACA,WAAW,SAAS,CAAC,SAAS,GAAG,WAAW,SAAS,CAAC,OAAO,GAAG;AAChE,WAAW,SAAS,CAAC,KAAK,GAAG;AAC7B,WAAW,SAAS,CAAC,OAAO,GAAG,QAAQ,QAAQ;AAC/C;;AAEA,GACA,MAAM;IACF,YACA;;IAEA,GACA,IAAI,EACJ;;IAEA,GACA,EAAE,EACF;;IAEA,GACA,KAAK,CAAE;QACH,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,KAAK,GAAG;IACjB;IACA;;IAEA,GACA,OAAO,OAAO,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE;QAC3B,OAAO,IAAI,MAAM,MAAM,IAAI;IAC/B;AACJ;AACA,SAAS,SAAS,CAAC,EAAE,CAAC;IAClB,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,EAAE,KAAK,CAAC,SAAS,GAAG,EAAE,KAAK,CAAC,SAAS;AACnE;AACA,MAAM;IACF,YAAY,IAAI,EAAE,EAAE,EAAE,KAAK,EAC3B,uDAAuD;IACvD,wDAAwD;IACxD,yCAAyC;IACzC,uDAAuD;IACvD,QAAQ,CAAE;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,GAAG,EAAE;IAAE;IACnD,iEAAiE;IACjE,2DAA2D;IAC3D,UAAU,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,CAAC,EAAE;QACnC,IAAI,MAAM,MAAM,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI;QACnC,IAAK,IAAI,KAAK,SAAS,KAAK,IAAI,MAAM,GAAI;YACtC,IAAI,MAAM,IACN,OAAO;YACX,IAAI,MAAM,AAAC,KAAK,MAAO;YACvB,IAAI,OAAO,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,IAAI;YAC3F,IAAI,OAAO,IACP,OAAO,QAAQ,IAAI,KAAK;YAC5B,IAAI,QAAQ,GACR,KAAK;iBAEL,KAAK,MAAM;QACnB;IACJ;IACA,QAAQ,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;QACzB,IAAK,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,SAAS,KAAI,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,WAAW,SAAS,KAAI,OAAO,IAAI,IAAI,GAAG,IAC/H,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE,MAAM,OACjE,OAAO;IACnB;IACA,IAAI,MAAM,EAAE,OAAO,EAAE;QACjB,IAAI,QAAQ,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,EAAE,SAAS,CAAC,GAAG,WAAW,CAAC;QAC7D,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,QAAQ,QAAQ,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,QAAQ,SAAS;YAChG,IAAI,WAAW,OAAO;gBAClB,IAAI,SAAS,QAAQ,MAAM,CAAC,SAAS,IAAI,SAAS,EAAE,IAAI,OAAO;gBAC/D,IAAI,UAAU,MACV;gBACJ,UAAU,QAAQ;gBAClB,IAAI,IAAI,SAAS,IAAI,IAAI,OAAO,EAAE;oBAC9B,QAAQ,QAAQ,MAAM,CAAC,SAAS,IAAI,OAAO;oBAC3C,IAAI,QAAQ,SACR;gBACR;YACJ,OACK;gBACD,UAAU,QAAQ,MAAM,CAAC,SAAS,IAAI,SAAS;gBAC/C,QAAQ,QAAQ,MAAM,CAAC,OAAO,IAAI,OAAO;gBACzC,IAAI,UAAU,SAAS,WAAW,SAAS,IAAI,SAAS,GAAG,KAAK,IAAI,OAAO,IAAI,GAC3E;YACR;YACA,IAAI,CAAC,QAAQ,WAAW,IAAI,OAAO,GAAG,IAAI,SAAS,IAAI,GACnD;YACJ,IAAI,SAAS,GACT,SAAS;YACb,IAAI,IAAI,KAAK,EACT,WAAW,KAAK,GAAG,CAAC,UAAU,QAAQ;YAC1C,MAAM,IAAI,CAAC;YACX,KAAK,IAAI,CAAC,UAAU;YACpB,GAAG,IAAI,CAAC,QAAQ;QACpB;QACA,OAAO;YAAE,QAAQ,MAAM,MAAM,GAAG,IAAI,MAAM,MAAM,IAAI,OAAO,YAAY;YAAM,KAAK;QAAO;IAC7F;AACJ;AACA;;;;;AAKA,GACA,MAAM;IACF,YACA;;IAEA,GACA,QAAQ,EACR;;IAEA,GACA,KAAK,EACL;;IAEA,GACA,SAAS,EACT;;IAEA,GACA,QAAQ,CAAE;QACN,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA;;IAEA,GACA,OAAO,OAAO,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE;QAChD,OAAO,IAAI,SAAS,UAAU,OAAO,WAAW;IACpD;IACA;;IAEA,GACA,IAAI,SAAS;QACT,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC/B,OAAO,OAAO,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;IAC7E;IACA;;IAEA,GACA,IAAI,OAAO;QACP,IAAI,IAAI,CAAC,OAAO,EACZ,OAAO;QACX,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI;QAC9B,KAAK,IAAI,SAAS,IAAI,CAAC,KAAK,CACxB,QAAQ,MAAM,KAAK,CAAC,MAAM;QAC9B,OAAO;IACX;IACA;;IAEA,GACA,SAAS,KAAK,EAAE;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM;IAC1D;IACA;;;;;;;;IAQA,GACA,OAAO,UAAU,EAAE;QACf,IAAI,EAAE,MAAM,EAAE,EAAE,OAAO,KAAK,EAAE,aAAa,CAAC,EAAE,WAAW,IAAI,CAAC,MAAM,EAAE,GAAG;QACzE,IAAI,SAAS,WAAW,MAAM;QAC9B,IAAI,IAAI,MAAM,IAAI,KAAK,CAAC,QACpB,OAAO,IAAI;QACf,IAAI,MACA,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC;QAC3B,IAAI,IAAI,CAAC,OAAO,EACZ,OAAO,IAAI,MAAM,GAAG,SAAS,EAAE,CAAC,OAAO,IAAI;QAC/C,IAAI,MAAM,IAAI,YAAY,IAAI,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,QAAQ,EAAE;QACpE,IAAI,UAAU,IAAI;QAClB,MAAO,IAAI,KAAK,IAAI,IAAI,IAAI,MAAM,CAAE;YAChC,IAAI,IAAI,IAAI,MAAM,IAAI,CAAC,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,IAAI,IAAI,SAAS,GAAG,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,GAAG;gBAC3F,IAAI,QAAQ,GAAG,CAAC,IAAI;gBACpB,IAAI,CAAC,QAAQ,QAAQ,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,KAAK,GACnD,MAAM,IAAI,CAAC;YACnB,OACK,IAAI,IAAI,UAAU,IAAI,KAAK,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,IAC9D,CAAC,KAAK,IAAI,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,UAAU,IAAI,GAAG,CAAC,EAAE,CAAC,IAAI,KAC/D,CAAC,CAAC,UAAU,aAAa,IAAI,CAAC,QAAQ,CAAC,IAAI,UAAU,KAAK,WAAW,IAAI,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,KAClG,QAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,GAAG;gBAC7E,IAAI,SAAS;YACjB,OACK;gBACD,IAAI,CAAC,UAAU,aAAa,IAAI,EAAE,IAAI,WAAW,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,KAAK,GAAG;oBAC9F,IAAI,CAAC,QAAQ,QAAQ,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,KAAK,GAC7C,MAAM,IAAI,CAAC,MAAM,MAAM,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,KAAK;gBAC3D;gBACA,IAAI,IAAI;YACZ;QACJ;QACA,OAAO,QAAQ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,MAAM,MAAM,GAAG,SAAS,KAAK,GAC7E,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAAE,KAAK;YAAO;YAAQ;YAAY;QAAS;IAC3E;IACA;;IAEA,GACA,IAAI,OAAO,EAAE;QACT,IAAI,QAAQ,KAAK,IAAI,IAAI,CAAC,OAAO,EAC7B,OAAO,IAAI;QACf,IAAI,SAAS,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW,CAAC;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;YACnD,IAAI,QAAQ,QAAQ,YAAY,CAAC,OAAO,QAAQ,MAAM,MAAM;YAC5D,IAAI,UAAU,OAAO;gBACjB,WAAW,KAAK,GAAG,CAAC,UAAU,MAAM,QAAQ;gBAC5C,OAAO,IAAI,CAAC;gBACZ,SAAS,IAAI,CAAC,QAAQ,MAAM,CAAC;YACjC,OACK,IAAI,UAAU,MAAM;gBACrB,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,GAAG,CAAC,OAAO;gBACvC,IAAI,QAAQ;oBACR,WAAW,KAAK,GAAG,CAAC,UAAU,OAAO,QAAQ;oBAC7C,OAAO,IAAI,CAAC;oBACZ,SAAS,IAAI,CAAC;gBAClB;YACJ;QACJ;QACA,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QAC9B,OAAO,OAAO,MAAM,IAAI,IAAI,OAAO,IAAI,SAAS,UAAU,QAAQ,QAAQ,SAAS,KAAK,EAAE;IAC9F;IACA;;;;;IAKA,GACA,QAAQ,IAAI,EAAE,EAAE,EAAE,CAAC,EAAE;QACjB,IAAI,IAAI,CAAC,OAAO,EACZ;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAK;YACxC,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,EAAE;YACnD,IAAI,MAAM,SAAS,QAAQ,QAAQ,MAAM,MAAM,IAC3C,MAAM,OAAO,CAAC,OAAO,OAAO,OAAO,KAAK,OAAO,OAAO,OACtD;QACR;QACA,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,IAAI;IACrC;IACA;;;IAGA,GACA,KAAK,OAAO,CAAC,EAAE;QACX,OAAO,WAAW,IAAI,CAAC;YAAC,IAAI;SAAC,EAAE,IAAI,CAAC;IACxC;IACA;;IAEA,GACA,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI;IAAE;IAC/C;;;IAGA,GACA,OAAO,KAAK,IAAI,EAAE,OAAO,CAAC,EAAE;QACxB,OAAO,WAAW,IAAI,CAAC,MAAM,IAAI,CAAC;IACtC;IACA;;;IAGA,GACA,OAAO,QAAQ,OAAO,EAAE,OAAO,EAC/B;;;IAGA,GACA,QAAQ,EAAE,UAAU,EACpB;;;IAGA,GACA,eAAe,CAAC,CAAC,EAAE;QACf,IAAI,IAAI,QAAQ,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,OAAO,IAAI,IAAI,QAAQ,IAAI;QAClF,IAAI,IAAI,QAAQ,MAAM,CAAC,CAAA,MAAO,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,OAAO,IAAI,IAAI,QAAQ,IAAI;QAClF,IAAI,eAAe,iBAAiB,GAAG,GAAG;QAC1C,IAAI,QAAQ,IAAI,WAAW,GAAG,cAAc;QAC5C,IAAI,QAAQ,IAAI,WAAW,GAAG,cAAc;QAC5C,SAAS,QAAQ,CAAC,CAAC,OAAO,OAAO,SAAW,QAAQ,OAAO,OAAO,OAAO,OAAO,QAAQ;QACxF,IAAI,SAAS,KAAK,IAAI,SAAS,MAAM,IAAI,GACrC,QAAQ,OAAO,GAAG,OAAO,GAAG,GAAG;IACvC;IACA;;;IAGA,GACA,OAAO,GAAG,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,EAAE,EAAE;QACtC,IAAI,MAAM,MACN,KAAK,WAAW,SAAS,MAAK;QAClC,IAAI,IAAI,QAAQ,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,OAAO,IAAI,QAAQ,OAAO,CAAC,OAAO;QACrE,IAAI,IAAI,QAAQ,MAAM,CAAC,CAAA,MAAO,CAAC,IAAI,OAAO,IAAI,QAAQ,OAAO,CAAC,OAAO;QACrE,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,EACpB,OAAO;QACX,IAAI,CAAC,EAAE,MAAM,EACT,OAAO;QACX,IAAI,eAAe,iBAAiB,GAAG;QACvC,IAAI,QAAQ,IAAI,WAAW,GAAG,cAAc,GAAG,IAAI,CAAC,OAAO,QAAQ,IAAI,WAAW,GAAG,cAAc,GAAG,IAAI,CAAC;QAC3G,OAAS;YACL,IAAI,MAAM,EAAE,IAAI,MAAM,EAAE,IACpB,CAAC,WAAW,MAAM,MAAM,EAAE,MAAM,MAAM,KACtC,MAAM,KAAK,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC,MAAM,KAAK,CAAC,GAC5D,OAAO;YACX,IAAI,MAAM,EAAE,GAAG,IACX,OAAO;YACX,MAAM,IAAI;YACV,MAAM,IAAI;QACd;IACJ;IACA;;;;;;IAMA,GACA,OAAO,MAAM,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EACrC;;;IAGA,GACA,eAAe,CAAC,CAAC,EAAE;QACf,IAAI,SAAS,IAAI,WAAW,MAAM,MAAM,cAAc,IAAI,CAAC,OAAO,MAAM;QACxE,IAAI,aAAa,OAAO,SAAS;QACjC,OAAS;YACL,IAAI,QAAQ,KAAK,GAAG,CAAC,OAAO,EAAE,EAAE;YAChC,IAAI,OAAO,KAAK,EAAE;gBACd,IAAI,SAAS,OAAO,cAAc,CAAC,OAAO,EAAE;gBAC5C,IAAI,YAAY,OAAO,SAAS,GAAG,OAAO,OAAO,MAAM,GAAG,IACpD,OAAO,KAAK,CAAC,SAAS,GAAG,IAAI,OAAO,MAAM,GACtC,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE;gBAClC,SAAS,KAAK,CAAC,KAAK,OAAO,OAAO,KAAK,EAAE,QAAQ,WAAW,OAAO,SAAS;gBAC5E,aAAa,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,QAAQ,OAAO,MAAM;YAC9D,OACK,IAAI,QAAQ,KAAK;gBAClB,SAAS,IAAI,CAAC,KAAK,OAAO,OAAO,MAAM,EAAE;gBACzC,aAAa,OAAO,OAAO,CAAC;YAChC;YACA,IAAI,OAAO,EAAE,GAAG,IACZ,OAAO,aAAa,CAAC,OAAO,KAAK,IAAI,OAAO,EAAE,GAAG,KAAK,IAAI,CAAC;YAC/D,MAAM,OAAO,EAAE;YACf,OAAO,IAAI;QACf;IACJ;IACA;;;;;;IAMA,GACA,OAAO,GAAG,MAAM,EAAE,OAAO,KAAK,EAAE;QAC5B,IAAI,QAAQ,IAAI;QAChB,KAAK,IAAI,SAAS,kBAAkB,QAAQ;YAAC;SAAO,GAAG,OAAO,SAAS,UAAU,OAC7E,MAAM,GAAG,CAAC,MAAM,IAAI,EAAE,MAAM,EAAE,EAAE,MAAM,KAAK;QAC/C,OAAO,MAAM,MAAM;IACvB;IACA;;IAEA,GACA,OAAO,KAAK,IAAI,EAAE;QACd,IAAI,CAAC,KAAK,MAAM,EACZ,OAAO,SAAS,KAAK;QACzB,IAAI,SAAS,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QAClC,IAAK,IAAI,IAAI,KAAK,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YACvC,IAAK,IAAI,QAAQ,IAAI,CAAC,EAAE,EAAE,SAAS,SAAS,KAAK,EAAE,QAAQ,MAAM,SAAS,CACtE,SAAS,IAAI,SAAS,MAAM,QAAQ,EAAE,MAAM,KAAK,EAAE,QAAQ,KAAK,GAAG,CAAC,MAAM,QAAQ,EAAE,OAAO,QAAQ;QAC3G;QACA,OAAO;IACX;AACJ;AACA;;AAEA,GACA,SAAS,KAAK,GAAG,WAAW,GAAE,IAAI,SAAS,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC;AAC1D,SAAS,SAAS,MAAM;IACpB,IAAI,OAAO,MAAM,GAAG,GAChB,IAAK,IAAI,OAAO,MAAM,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACtD,IAAI,MAAM,MAAM,CAAC,EAAE;QACnB,IAAI,SAAS,MAAM,OAAO,GACtB,OAAO,OAAO,KAAK,GAAG,IAAI,CAAC;QAC/B,OAAO;IACX;IACJ,OAAO;AACX;AACA,SAAS,KAAK,CAAC,SAAS,GAAG,SAAS,KAAK;AACzC;;;;AAIA,GACA,MAAM;IACF,YAAY,SAAS,EAAE;QACnB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ;QACxE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU;QAClC,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ;QAC3D,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,WAAW;YACX,IAAI,CAAC,IAAI,GAAG,EAAE;YACd,IAAI,CAAC,EAAE,GAAG,EAAE;YACZ,IAAI,CAAC,KAAK,GAAG,EAAE;QACnB;IACJ;IACA;;IAEA,GACA,aAAc;QACV,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG,CAAC,WAAW,SAAS;QACrC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,SAAS;QACnC,IAAI,CAAC,IAAI,GAAG,EAAE;QACd,IAAI,CAAC,EAAE,GAAG,EAAE;QACZ,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;;;IAGA,GACA,IAAI,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE;QACjB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,QACzB,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,EAAE,GAAG,CAAC,MAAM,IAAI;IACjF;IACA;;IAEA,GACA,SAAS,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE;QACtB,IAAI,OAAO,OAAO,IAAI,CAAC,MAAM,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO;QACpE,IAAI,QAAQ,KAAK,CAAC,OAAO,IAAI,CAAC,QAAQ,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,GAC/E,MAAM,IAAI,MAAM;QACpB,IAAI,OAAO,GACP,OAAO;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,eAAe,KACvC,IAAI,CAAC,WAAW,CAAC;QACrB,IAAI,IAAI,CAAC,UAAU,GAAG,GAClB,IAAI,CAAC,UAAU,GAAG;QACtB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU;QACrC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU;QACjC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;QAChB,IAAI,MAAM,KAAK,EACX,IAAI,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK;QACjD,OAAO;IACX;IACA;;IAEA,GACA,SAAS,IAAI,EAAE,KAAK,EAAE;QAClB,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,GACvE,OAAO;QACX,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAChB,IAAI,CAAC,WAAW,CAAC;QACrB,IAAI,CAAC,WAAW,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,QAAQ;QAC5D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnB,IAAI,OAAO,MAAM,KAAK,CAAC,MAAM,GAAG;QAChC,IAAI,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,KAAK;QAC7B,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,KAAK,GAAG;QACnC,IAAI,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC,KAAK,GAAG;QAC/B,OAAO;IACX;IACA;;;IAGA,GACA,SAAS;QAAE,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,KAAK;IAAG;IACpD;;IAEA,GACA,YAAY,IAAI,EAAE;QACd,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,EAChB,IAAI,CAAC,WAAW,CAAC;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,GACtB,OAAO;QACX,IAAI,SAAS,SAAS,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,MAAM,IAAI,CAAC,WAAW;QACnI,IAAI,CAAC,IAAI,GAAG,MAAM,+CAA+C;QACjE,OAAO;IACX;AACJ;AACA,SAAS,iBAAiB,CAAC,EAAE,CAAC,EAAE,QAAQ;IACpC,IAAI,MAAM,IAAI;IACd,KAAK,IAAI,OAAO,EACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,IAClC,IAAI,IAAI,KAAK,CAAC,EAAE,CAAC,QAAQ,IAAI,GACzB,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE,EAAE,IAAI,QAAQ,CAAC,EAAE;IACjD,IAAI,SAAS,IAAI;IACjB,KAAK,IAAI,OAAO,EACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE,IAAK;QACvC,IAAI,QAAQ,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE;QAChC,IAAI,SAAS,QAAQ,CAAC,WAAW,SAAS,MAAM,CAAC,SAAS,KAAK,KAAK,IAAI,QAAQ,CAAC,EAAE,IAC/E,CAAC,CAAC,aAAa,QAAQ,aAAa,KAAK,IAAI,KAAK,IAAI,SAAS,YAAY,CAAC,OAAO,QAAQ,IAAI,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,GAC/G,OAAO,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE;IAC/B;IACJ,OAAO;AACX;AACA,MAAM;IACF,YAAY,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAE;QACzC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;IAAG;IAChE,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG;IAAG;IAC5D,KAAK,GAAG,EAAE,OAAO,CAAC,WAAW,SAAS,GAAV,EAAc;QACtC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG;QACpC,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;QAC1B,OAAO,IAAI;IACf;IACA,UAAU,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE;QAC1B,MAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAE;YAC9C,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;YAC5C,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAC7B,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,IAAI,OACvC,KAAK,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAC7B;YACJ,IAAI,CAAC,UAAU;YACf,UAAU;QACd;QACA,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;YAC3C,IAAI,aAAa,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,MAAM;YAC/G,IAAI,CAAC,WAAW,IAAI,CAAC,UAAU,GAAG,YAC9B,IAAI,CAAC,aAAa,CAAC;QAC3B;QACA,IAAI,CAAC,IAAI;IACb;IACA,QAAQ,GAAG,EAAE,IAAI,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI,GACzC,IAAI,CAAC,SAAS,CAAC,KAAK,MAAM;IAClC;IACA,OAAO;QACH,OAAS;YACL,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE;gBAC5C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,WAAW,SAAS;gBAC1C,IAAI,CAAC,KAAK,GAAG;gBACb;YACJ,OACK;gBACD,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC9F,IAAI,OAAO,WAAW,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC;gBACjD,IAAI,CAAC,IAAI,GAAG;gBACZ,IAAI,CAAC,EAAE,GAAG,WAAW,MAAM,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC9C,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;gBACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,GAAG;gBACrC,IAAI,IAAI,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAC7E;YACR;QACJ;IACJ;IACA,cAAc,KAAK,EAAE;QACjB,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,EAAE;YACzD,IAAI,CAAC,UAAU;YACf,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,MAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAC/F,IAAI,CAAC,UAAU;YACvB;YACA,IAAI,CAAC,UAAU,GAAG;QACtB,OACK;YACD,IAAI,CAAC,UAAU,GAAG;QACtB;IACJ;IACA,YAAY;QACR,IAAI,CAAC,UAAU;QACf,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI;IACb;IACA,QAAQ,KAAK,EAAE;QACX,OAAO,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI,IACvF,IAAI,CAAC,EAAE,GAAG,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,GAAG,MAAM,OAAO;IAC1D;AACJ;AACA,MAAM;IACF,YAAY,IAAI,CAAE;QACd,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,OAAO,KAAK,IAAI,EAAE,OAAO,IAAI,EAAE,WAAW,CAAC,CAAC,EAAE;QAC1C,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,IAAK,IAAI,MAAM,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,OAAO,EAAE,MAAM,IAAI,SAAS,CAAE;gBACvD,IAAI,IAAI,QAAQ,IAAI,UAChB,KAAK,IAAI,CAAC,IAAI,YAAY,KAAK,MAAM,UAAU;YACvD;QACJ;QACA,OAAO,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,WAAW;IACvD;IACA,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG;IAAG;IAChE,KAAK,GAAG,EAAE,OAAO,CAAC,WAAW,SAAS,GAAV,EAAc;QACtC,KAAK,IAAI,OAAO,IAAI,CAAC,IAAI,CACrB,IAAI,IAAI,CAAC,KAAK;QAClB,IAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,KAAK,GAAG,IACxC,WAAW,IAAI,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,IAAI;QACT,OAAO,IAAI;IACf;IACA,QAAQ,GAAG,EAAE,IAAI,EAAE;QACf,KAAK,IAAI,OAAO,IAAI,CAAC,IAAI,CACrB,IAAI,OAAO,CAAC,KAAK;QACrB,IAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG,KAAK,GAAG,IACxC,WAAW,IAAI,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,IAAI,GAC/C,IAAI,CAAC,IAAI;IACjB;IACA,OAAO;QACH,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,GAAG;YACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,GAAG,WAAW,SAAS;YAC1C,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,IAAI,GAAG,CAAC;QACjB,OACK;YACD,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE;YACtB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI;YACpB,IAAI,CAAC,EAAE,GAAG,IAAI,EAAE;YAChB,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK;YACtB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI;YACpB,IAAI,IAAI,KAAK,EACT,IAAI,IAAI;YACZ,WAAW,IAAI,CAAC,IAAI,EAAE;QAC1B;IACJ;AACJ;AACA,SAAS,WAAW,IAAI,EAAE,KAAK;IAC3B,IAAK,IAAI,MAAM,IAAI,CAAC,MAAM,GAAI;QAC1B,IAAI,aAAa,CAAC,SAAS,CAAC,IAAI;QAChC,IAAI,cAAc,KAAK,MAAM,EACzB;QACJ,IAAI,QAAQ,IAAI,CAAC,WAAW;QAC5B,IAAI,aAAa,IAAI,KAAK,MAAM,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG;YAC1E,QAAQ,IAAI,CAAC,aAAa,EAAE;YAC5B;QACJ;QACA,IAAI,IAAI,OAAO,CAAC,SAAS,GACrB;QACJ,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,QAAQ;IACZ;AACJ;AACA,MAAM;IACF,YAAY,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAE;QAC9B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,yCAAyC;QACzC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,EAAE,GAAG,CAAC,WAAW,SAAS;QAC/B,IAAI,CAAC,OAAO,GAAG;QACf,iEAAiE;QACjE,wBAAwB;QACxB,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,CAAC,MAAM,MAAM;IAC9C;IACA,KAAK,GAAG,EAAE,OAAO,CAAC,WAAW,SAAS,GAAV,EAAc;QACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK;QACtB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG;QACrE,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,IAAI;QACT,OAAO,IAAI;IACf;IACA,QAAQ,GAAG,EAAE,IAAI,EAAE;QACf,MAAO,IAAI,CAAC,SAAS,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,GAAG,IAAI,IAAI,EAChH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK;IAC7B;IACA,aAAa,KAAK,EAAE;QAChB,OAAO,IAAI,CAAC,MAAM,EAAE;QACpB,OAAO,IAAI,CAAC,QAAQ,EAAE;QACtB,OAAO,IAAI,CAAC,UAAU,EAAE;QACxB,IAAI,CAAC,SAAS,GAAG,aAAa,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ;IAC5D;IACA,UAAU,SAAS,EAAE;QACjB,IAAI,IAAI,GAAG,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM;QAC5C,oDAAoD;QACpD,MAAO,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,EACxF;QACJ,OAAO,IAAI,CAAC,MAAM,EAAE,GAAG;QACvB,OAAO,IAAI,CAAC,QAAQ,EAAE,GAAG;QACzB,OAAO,IAAI,CAAC,UAAU,EAAE,GAAG;QAC3B,IAAI,WACA,OAAO,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;QACzC,IAAI,CAAC,SAAS,GAAG,aAAa,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ;IAC5D;IACA,mEAAmE;IACnE,oEAAoE;IACpE,OAAO;QACH,IAAI,OAAO,IAAI,CAAC,EAAE,EAAE,WAAW,IAAI,CAAC,KAAK;QACzC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,YAAY,IAAI,CAAC,SAAS,GAAG,IAAI,EAAE,GAAG;QAC1C,OAAS;YACL,IAAI,IAAI,IAAI,CAAC,SAAS;YACtB,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,GAAG;gBACvG,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM;oBACzB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;oBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO;oBACrC;gBACJ;gBACA,IAAI,CAAC,YAAY,CAAC;gBAClB,IAAI,WACA,OAAO,WAAW;YAC1B,OACK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;gBACzB,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,OAAO,GAAG,WAAW,SAAS;gBAC7C;YACJ,OACK,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM;gBAC9B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBAC1B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS;gBACpC;YACJ,OACK;gBACD,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,KAAK;gBAC/B,IAAI,CAAC,QAAQ,KAAK,EAAE;oBAChB,IAAI,CAAC,SAAS,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,IAAI;gBACpB,OACK,IAAI,YAAY,IAAI,CAAC,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE;oBACjF,8EAA8E;oBAC9E,IAAI,CAAC,MAAM,CAAC,IAAI;gBACpB,OACK;oBACD,IAAI,CAAC,KAAK,GAAG;oBACb,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;oBACjC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;oBACjC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE;oBACxB,IAAI,CAAC,OAAO,GAAG,QAAQ,OAAO;oBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI;oBAChB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO;oBAClC;gBACJ;YACJ;QACJ;QACA,IAAI,WAAW;YACX,IAAI,CAAC,SAAS,GAAG;YACjB,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,KAAK,SAAS,CAAC,EAAE,GAAG,MAAM,IAC9D,IAAI,CAAC,SAAS;QACtB;IACJ;IACA,eAAe,EAAE,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EACnB,OAAO,IAAI,CAAC,MAAM;QACtB,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC9C,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,SAAS,EACnC;YACJ,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAC/F,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;QAClC;QACA,OAAO,OAAO,OAAO;IACzB;IACA,QAAQ,EAAE,EAAE;QACR,IAAI,OAAO;QACX,IAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,IAAI,IACpE;QACJ,OAAO;IACX;AACJ;AACA,SAAS,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IACrD,EAAE,IAAI,CAAC;IACP,EAAE,IAAI,CAAC;IACP,IAAI,OAAO,SAAS;IACpB,IAAI,MAAM,QAAQ,OAAO,SAAS;IAClC,OAAS;QACL,IAAI,OAAO,AAAC,EAAE,EAAE,GAAG,OAAQ,EAAE,EAAE,EAAE,OAAO,QAAQ,EAAE,OAAO,GAAG,EAAE,OAAO;QACrE,IAAI,MAAM,OAAO,IAAI,EAAE,EAAE,GAAG,OAAO,EAAE,EAAE,EAAE,UAAU,KAAK,GAAG,CAAC,KAAK;QACjE,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,EAAE;YACpB,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAClE,WAAW,EAAE,cAAc,CAAC,EAAE,EAAE,GAAG,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,GAC1D,WAAW,YAAY,CAAC,KAAK,SAAS,EAAE,KAAK,EAAE,EAAE,KAAK;QAC9D,OACK;YACD,IAAI,UAAU,OAAO,CAAC,WAAW,EAAE,MAAM,EAAE,EAAE,MAAM,GAC/C,WAAW,YAAY,CAAC,KAAK,SAAS,EAAE,MAAM,EAAE,EAAE,MAAM;QAChE;QACA,IAAI,MAAM,MACN;QACJ,IAAI,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,WAAW,WAAW,EAC1D,WAAW,WAAW,CAAC;QAC3B,MAAM;QACN,IAAI,QAAQ,GACR,EAAE,IAAI;QACV,IAAI,QAAQ,GACR,EAAE,IAAI;IACd;AACJ;AACA,SAAS,WAAW,CAAC,EAAE,CAAC;IACpB,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,EACpB,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAC1B,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAC7B,OAAO;IACf,OAAO;AACX;AACA,SAAS,OAAO,KAAK,EAAE,KAAK;IACxB,IAAK,IAAI,IAAI,OAAO,IAAI,MAAM,MAAM,GAAG,GAAG,IAAI,GAAG,IAC7C,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE;IAC3B,MAAM,GAAG;AACb;AACA,SAAS,OAAO,KAAK,EAAE,KAAK,EAAE,KAAK;IAC/B,IAAK,IAAI,IAAI,MAAM,MAAM,GAAG,GAAG,KAAK,OAAO,IACvC,KAAK,CAAC,IAAI,EAAE,GAAG,KAAK,CAAC,EAAE;IAC3B,KAAK,CAAC,MAAM,GAAG;AACnB;AACA,SAAS,aAAa,KAAK,EAAE,KAAK;IAC9B,IAAI,QAAQ,CAAC,GAAG,WAAW,WAAW,SAAS;IAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAC9B,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,YAAY,KAAK,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,IAAI,GAAG;QACtE,QAAQ;QACR,WAAW,KAAK,CAAC,EAAE;IACvB;IACJ,OAAO;AACX;AAEA;;;AAGA,GACA,SAAS,YAAY,MAAM,EAAE,OAAO,EAAE,KAAK,OAAO,MAAM;IACpD,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAI,OAAO,MAAM,EAAG;QAC1C,IAAI,OAAO,UAAU,CAAC,MAAM,GAAG;YAC3B,KAAK,UAAW,IAAI;YACpB;QACJ,OACK;YACD;YACA,IAAI,iBAAiB,QAAQ;QACjC;IACJ;IACA,OAAO;AACX;AACA;;;;;;AAMA,GACA,SAAS,WAAW,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,IAAK;QACrB,IAAI,KAAK,KACL,OAAO;QACX,IAAI,KAAK,OAAO,MAAM,EAClB;QACJ,KAAK,OAAO,UAAU,CAAC,MAAM,IAAI,UAAW,IAAI,UAAW;QAC3D,IAAI,iBAAiB,QAAQ;IACjC;IACA,OAAO,WAAW,OAAO,CAAC,IAAI,OAAO,MAAM;AAC/C", "ignoreList": [0], "debugId": null}}]}