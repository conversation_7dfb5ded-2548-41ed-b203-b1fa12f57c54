"use client";

import { useEffect } from "react";
import { Menu } from "./menu";
import { Sidebar } from "./sidebar";
import { Editor } from "./editor";
import { FileTabs } from "./file-tabs";
import { Output } from "./output";
import { useReplStore } from "@/lib/stores/repl-store";
import { tryPlausible } from "@/lib/utilities";
import SplitPane from "react-split-pane";

interface ReplProps {
  enableSidebar?: boolean;
  enableMenu?: boolean;
}

export function Repl({ enableSidebar = true, enableMenu = true }: ReplProps) {
  const { isSaved, setRunCode } = useReplStore();

  const handleShare = async () => {
    tryPlausible("Share");
    await navigator.clipboard.writeText(window.location.toString());
  };

  const handleRun = async () => {
    tryPlausible("Compile");
    setRunCode(true);
  };

  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    if (!isSaved) {
      e.preventDefault();
      e.returnValue = "";
    }
  };

  useEffect(() => {
    if (!isSaved) {
      window.addEventListener("beforeunload", handleBeforeUnload);
      return () => {
        window.removeEventListener("beforeunload", handleBeforeUnload);
      };
    }
  }, [isSaved]);

  return (
    <div className="w-full h-screen font-sans flex flex-col overflow-hidden">
      {enableMenu && <Menu onShare={handleShare} onRun={handleRun} />}
      <div className="flex items-stretch flex-1 overflow-hidden">
        {enableSidebar && <Sidebar />}
        <div className="flex-1 overflow-hidden">
          <SplitPane split="horizontal" minSize={64} defaultSize="50%">
            <div className="h-full flex flex-col">
              <div className="text-sm">
                <FileTabs />
              </div>
              <Editor />
            </div>
            <div className="border-t border-stone-200 dark:border-stone-700 overflow-hidden">
              <Output />
            </div>
          </SplitPane>
        </div>
      </div>
    </div>
  );
}
