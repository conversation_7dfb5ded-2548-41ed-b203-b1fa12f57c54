'use client';

import { useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useReplStore } from '@/lib/stores/repl-store';
import { useFavouritesStore } from '@/lib/stores/repl-store';

export function FavouriteButton() {
  const { 
    fiddleTitle, 
    fiddleUpdated, 
    files, 
    favouriteIndex, 
    setFavouriteIndex 
  } = useReplStore();
  
  const { favourites, setFavourites } = useFavouritesStore();
  
  const isFavourite = favouriteIndex !== -1;

  const toggle = () => {
    if (isFavourite) {
      const newFavourites = favourites.filter((_fiddle, i) => i !== favouriteIndex);
      setFavourites(newFavourites);
      setFavouriteIndex(-1);
    } else {
      const newFavourite = {
        title: fiddleTitle,
        updated: fiddleUpdated,
        files: files
      };
      const newFavourites = [newFavourite, ...favourites];
      setFavourites(newFavourites);
      setFavouriteIndex(0);
    }
  };

  // Keep favourites consistent with fiddle data
  useEffect(() => {
    if (isFavourite && favourites[favouriteIndex]) {
      const updatedFavourites = [...favourites];
      updatedFavourites[favouriteIndex] = {
        title: fiddleTitle,
        updated: fiddleUpdated,
        files: files
      };
      setFavourites(updatedFavourites);
    }
  }, [fiddleTitle, fiddleUpdated, files, isFavourite, favouriteIndex, favourites, setFavourites]);

  return (
    <button 
      onClick={toggle} 
      className={`${isFavourite ? 'text-red-600 dark:text-red-400' : ''}`}
    >
      <Icon icon="mi:heart" className="w-6 h-6" />
      <span className="sr-only">
        {isFavourite ? 'Remove from favorites' : 'Add to favorites'}
      </span>
    </button>
  );
}
