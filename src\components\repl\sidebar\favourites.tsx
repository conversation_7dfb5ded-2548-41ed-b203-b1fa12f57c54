'use client';

import { useRouter } from 'next/navigation';
import TimeAgo from 'react-timeago';
import { compress, type Fiddle } from '@/lib/compress-fiddle';
import { useReplStore } from '@/lib/stores/repl-store';
import { useFavouritesStore } from '@/lib/stores/repl-store';

export function Favourites() {
  const router = useRouter();
  const { 
    favouriteIndex, 
    setFavouriteIndex, 
    setFiles, 
    setFiddleTitle, 
    setFiddleUpdated 
  } = useReplStore();
  const { favourites } = useFavouritesStore();

  const openFavourite = (favourite: Fiddle, index: number) => {
    if (index === favouriteIndex) {
      return;
    }
    
    setFavouriteIndex(index);
    setFiles(favourite.files);
    setFiddleTitle(favourite.title);
    setFiddleUpdated(favourite.updated);
    
    // Update URL with fragment
    const fiddleFragmentURL = compress(favourite);
    router.push(`/#${fiddleFragmentURL}`);
  };

  return (
    <>
      <p className="text-sm p-3 text-stone-500 dark:text-stone-400 leading-tight">
        Click the heart icon on a fiddle to have it appear in this list.
      </p>

      <ul className="text-stone-600 dark:text-stone-200 text-sm">
        {favourites.map((fiddle, index) => (
          <li key={index}>
            <button
              className="w-full text-left flex items-center px-4 py-2 hover:bg-stone-200 dark:hover:bg-stone-900"
              onClick={() => openFavourite(fiddle, index)}
            >
              <div className="grow">{fiddle.title || 'Untitled'}</div>
              {fiddle.updated && (
                <div className="text-xs opacity-50">
                  <TimeAgo date={fiddle.updated} />
                </div>
              )}
            </button>
          </li>
        ))}
      </ul>
    </>
  );
}
