'use client';

import { useState, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { Favourites } from './favourites';
import { Examples } from './examples';

interface SidebarOptionsProps {
  forceClose: boolean;
  onSelectOption: () => void;
}

export function SidebarOptions({ forceClose, onSelectOption }: SidebarOptionsProps) {
  const [selectedOptionIndex, setSelectedOptionIndex] = useState(-1);

  const options = [
    {
      label: typeof navigator !== 'undefined' && navigator.language === 'en-GB' ? 'Favourites' : 'Favorites',
      icon: 'mi:heart',
      color: 'red'
    },
    {
      label: 'Examples',
      icon: 'mi:archive',
      color: 'teal'
    }
  ];

  const selectedOption = selectedOptionIndex === -1 ? undefined : options[selectedOptionIndex];

  useEffect(() => {
    if (forceClose) {
      setSelectedOptionIndex(-1);
    }
  }, [forceClose]);

  useEffect(() => {
    if (selectedOptionIndex !== -1) {
      onSelectOption();
    }
  }, [selectedOptionIndex, onSelectOption]);

  if (selectedOption) {
    return (
      <>
        <div className="border-y dark:border-stone-700">
          <button
            className="flex items-center gap-3 px-2.5 h-10 w-full font-medium"
            onClick={() => setSelectedOptionIndex(-1)}
          >
            <Icon icon="mi:chevron-left" className="w-5 h-5" />
            {selectedOption.label}
          </button>
        </div>
        {selectedOptionIndex === 0 && <Favourites />}
        {selectedOptionIndex === 1 && <Examples />}
      </>
    );
  }

  return (
    <ul>
      {options.map((option, index) => (
        <li key={option.label} className="border-b first:border-t dark:border-stone-700">
          <button
            className={`group flex items-center gap-3 px-2.5 h-10 w-full text-stone-600 dark:text-stone-400 ${
              option.color === 'red' 
                ? 'hover:text-red-600 dark:hover:text-red-400' 
                : 'hover:text-teal-600 dark:hover:text-teal-400'
            }`}
            onClick={() => setSelectedOptionIndex(index)}
          >
            <Icon icon={option.icon} className="w-5 h-5" />
            <span className="grow text-left font-medium">{option.label}</span>
            <Icon icon="mi:chevron-right" className="opacity-0 group-hover:opacity-100" />
          </button>
        </li>
      ))}
    </ul>
  );
}
