'use client';

import { useState, useRef, useEffect } from 'react';
import { Icon } from '@iconify/react';
import { useReplStore, type File } from '@/lib/stores/repl-store';

interface FileTabProps {
  file: File;
  canEdit: boolean;
}

export function FileTab({ file, canEdit }: FileTabProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [path, setPath] = useState('');
  const editableRef = useRef<HTMLButtonElement>(null);
  
  const { files, selectedFilePath, setFiles, setSelectedFilePath } = useReplStore();
  
  const isSelected = selectedFilePath === file.path;

  // Update path when not editing
  useEffect(() => {
    if (!isEditing) {
      setPath(file.path.substring(0, file.path.lastIndexOf('.')));
    }
  }, [file.path, isEditing]);

  const handleClick = () => {
    setSelectedFilePath(file.path);
  };

  const handleEditClick = () => {
    if (isSelected) {
      setIsEditing(true);
    }
  };

  const handleFocusIn = () => {
    setIsEditing(true);
  };

  const handleFocusOut = () => {
    setIsEditing(false);
    const newPath = path + '.java';
    
    if (newPath !== file.path) {
      const existingFile = files.find((f) => f.path === newPath);
      if (existingFile) {
        alert(`A file with the name '${path}' already exists!`);
        return;
      }

      const newFiles = files.map((f) => {
        if (f.path === file.path) {
          return { ...f, path: newPath };
        }
        return f;
      });
      
      setFiles(newFiles);
      setSelectedFilePath(newPath);
    }
  };

  const handleDeleteClick = () => {
    if (!confirm(`Are you sure you want to delete '${path}'?`)) {
      return;
    }
    
    const newFiles = files.filter((f) => f.path !== file.path);
    setFiles(newFiles);
    setSelectedFilePath(newFiles[0].path);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      editableRef.current?.blur();
    }
  };

  return (
    <button
      className={`group px-3 py-2 hover:text-stone-800 dark:hover:text-stone-400 cursor-pointer flex items-center justify-center ${
        isSelected 
          ? 'text-stone-600 dark:text-stone-200' 
          : ''
      }`}
      onClick={handleClick}
    >
      <button
        ref={editableRef}
        onClick={handleEditClick}
        onFocus={handleFocusIn}
        onBlur={handleFocusOut}
        onKeyDown={handleKeyDown}
        className={`focus:text-orange-600 ring-0 ${
          !isSelected || !canEdit ? 'pointer-events-none' : ''
        } ${isSelected ? 'cursor-text' : ''}`}
        contentEditable={isEditing}
        suppressContentEditableWarning={true}
        onInput={(e) => setPath(e.currentTarget.textContent || '')}
      >
        {path}
      </button>
      {canEdit && (
        <button
          onClick={handleDeleteClick}
          className={`ml-1 -mr-3 opacity-0 group-hover:opacity-50 ${
            !isSelected ? 'opacity-0 pointer-events-none' : ''
          }`}
        >
          <Icon icon="mi:close" className="w-3 h-3" />
        </button>
      )}
    </button>
  );
}
