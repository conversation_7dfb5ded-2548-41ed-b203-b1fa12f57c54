"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useReplStore } from "@/lib/stores/repl-store";
import {
  decompress,
  type Fiddle,
  defaultFiddle,
  defaultFiddleComp,
} from "@/lib/compress-fiddle";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const router = useRouter();
  const { fiddleTitle, setFiles, setFiddleTitle, setFiddleUpdated } =
    useReplStore();

  useEffect(() => {
    function setFiddle() {
      const fragmentURL: string = window.location.hash.slice(1);
      let fiddle: Fiddle;

      if (!fragmentURL) {
        fiddle = defaultFiddle;
        router.replace(`/#${defaultFiddleComp}`);
      } else {
        try {
          fiddle = decompress(fragmentURL);
        } catch (error) {
          console.error("Error decompressing fiddle:", error);
          fiddle = defaultFiddle;
          router.replace(`/#${defaultFiddleComp}`);
        }
      }

      setFiles(fiddle.files);
      setFiddleTitle(fiddle.title);
      setFiddleUpdated(fiddle.updated);
    }

    // Set initial fiddle
    setFiddle();

    // Listen for hash changes
    const handlePopState = () => setFiddle();
    window.addEventListener("popstate", handlePopState);

    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [router, setFiles, setFiddleTitle, setFiddleUpdated]);

  // Update document title dynamically
  useEffect(() => {
    const title = fiddleTitle
      ? `${fiddleTitle} - JavaFiddle`
      : "JavaFiddle - Build and share Java code snippets in your browser";
    document.title = title;
  }, [fiddleTitle]);

  return (
    <div className="bg-white text-black dark:bg-stone-900 dark:text-white">
      {children}
    </div>
  );
}
