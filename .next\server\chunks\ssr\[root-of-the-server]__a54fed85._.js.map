{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/app-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/app-layout.tsx <module evaluation>\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+DACA", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/app-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppLayout() from the server but AppLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/app-layout.tsx\",\n    \"AppLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,2CACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/repl.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Repl = registerClientReference(\n    function() { throw new Error(\"Attempted to call Repl() from the server but Repl is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/repl/repl.tsx <module evaluation>\",\n    \"Repl\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8DACA", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/repl.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Repl = registerClientReference(\n    function() { throw new Error(\"Attempted to call Repl() from the server but Repl is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/repl/repl.tsx\",\n    \"Repl\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,0CACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/app/page.tsx"], "sourcesContent": ["import { AppLayout } from \"@/components/app-layout\";\nimport { Repl } from \"@/components/repl/repl\";\n\nexport default function Home() {\n  return (\n    <AppLayout>\n      <Repl />\n    </AppLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,YAAS;kBACR,cAAA,8OAAC,kIAAA,CAAA,OAAI;;;;;;;;;;AAGX", "debugId": null}}]}