'use client';

import { FileTab } from './file-tab';
import { useReplStore } from '@/lib/stores/repl-store';
import { Icon } from '@iconify/react';

export function FileTabs() {
  const { files, setFiles, setSelectedFilePath } = useReplStore();

  const getNewFilePath = (): string => {
    let i = 2;
    let path = `Class.java`;
    while (files.find((f) => f.path === path)) {
      path = `Class${i}.java`;
      i++;
    }
    return path;
  };

  const handleAddFile = () => {
    const path = getNewFilePath();
    const className = path.replace(/\.java$/, '');
    const newFiles = [
      ...files,
      {
        path,
        content: `package fiddle;

class ${className} {
    public ${className}() {
        // TODO
    }
}
`
      }
    ];
    setFiles(newFiles);
    setSelectedFilePath(path);
  };

  return (
    <div className="flex items-stretch text-stone-500 h-8">
      {files.map((file, i) => (
        <FileTab key={file.path} file={file} canEdit={i > 0} />
      ))}
      <button
        className="mx-1"
        title="New file"
        onClick={handleAddFile}
      >
        <Icon icon="mi:add" className="w-4 h-4 ml-2" />
      </button>
    </div>
  );
}
