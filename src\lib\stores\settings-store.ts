import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export type Theme = 'system' | 'light' | 'dark';

interface SettingsState {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  getEffectiveTheme: () => 'light' | 'dark';
}

export const useSettingsStore = create<SettingsState>()(
  persist(
    (set, get) => ({
      theme: 'system',
      setTheme: (theme) => set({ theme }),
      getEffectiveTheme: () => {
        const { theme } = get();
        
        // Check for URL parameter override
        if (typeof window !== 'undefined') {
          const params = new URLSearchParams(window.location.search);
          const urlTheme = params.get('theme');
          if (urlTheme === 'light' || urlTheme === 'dark') {
            return urlTheme;
          }
        }
        
        if (theme === 'system') {
          if (typeof window !== 'undefined') {
            return window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'dark';
          }
          return 'dark'; // Default for SSR
        }
        
        return theme;
      },
    }),
    {
      name: 'javafiddle-settings-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Hook to get effective theme with reactivity
export const useEffectiveTheme = () => {
  const { theme, getEffectiveTheme } = useSettingsStore();
  return getEffectiveTheme();
};
