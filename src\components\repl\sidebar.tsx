'use client';

import { Icon } from '@iconify/react';
import { useTheme } from 'next-themes';
import { useReplStore } from '@/lib/stores/repl-store';
import { SidebarOptions } from './sidebar/sidebar-options';

export function Sidebar() {
  const { theme } = useTheme();
  const { isSidebarOpen, setIsSidebarOpen } = useReplStore();

  // Import logos dynamically based on theme
  const cheerpjLogotypeUrl = theme === 'dark' 
    ? '/assets/cheerpj/logotype-white.svg' 
    : '/assets/cheerpj/logotype-grey.svg';

  return (
    <aside className={`w-10 bg-stone-100 dark:bg-stone-800 flex-shrink-0 transition-[width] flex flex-col overflow-hidden ${isSidebarOpen ? '!w-80' : ''}`}>
      <div className={`text-right shadow-none animate-shadow ${isSidebarOpen ? '!shadow' : ''}`}>
        <button
          className="w-10 h-10 text-stone-600 hover:text-stone-900 dark:text-stone-400 dark:hover:text-stone-100 inline-flex items-center justify-center"
          onClick={() => setIsSidebarOpen(!isSidebarOpen)}
        >
          {isSidebarOpen ? (
            <Icon icon="mi:close" className="w-4 h-4" />
          ) : (
            <Icon icon="mi:menu" className="w-5 h-5" />
          )}
        </button>
      </div>

      <div className="w-80 grow overflow-hidden">
        <div className="h-1/2 overflow-y-auto flex flex-col">
          <SidebarOptions
            forceClose={!isSidebarOpen}
            onSelectOption={() => setIsSidebarOpen(true)}
          />
        </div>
        {isSidebarOpen && (
          <div className="h-1/2 overflow-y-auto flex flex-col">
            <div className="grow p-4 leading-tight bg-stone-200 text-stone-700 dark:bg-stone-700 dark:text-stone-300 text-sm">
              <p>
                JavaFiddle is an online tool to <b>build</b> and <b>share</b> snippets of Java code.
              </p>

              <hr className="my-6 border-stone-300 dark:border-stone-600" />

              <ul className="list-disc space-y-2 ml-3">
                <li>
                  Runs entirely <b>in your browser</b>.
                </li>
                <li>Supports all of Java SE 8, including Swing.</li>
              </ul>

              <hr className="my-6 border-stone-300 dark:border-stone-600" />

              <p>
                Powered by{' '}
                <a
                  href="https://leaningtech.com/cheerpj/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline"
                >
                  CheerpJ
                </a>
              </p>

              <div className="mt-4">
                <img 
                  src={cheerpjLogotypeUrl} 
                  alt="CheerpJ" 
                  className="h-6" 
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </aside>
  );
}
