'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { compress, type Fiddle } from '@/lib/compress-fiddle';
import { useReplStore } from '@/lib/stores/repl-store';
import { Loading } from '@/components/loading';

export function Examples() {
  const [examples, setExamples] = useState<Fiddle[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  
  const { 
    favouriteIndex, 
    setFavouriteIndex, 
    setFiles, 
    setFiddleTitle, 
    setFiddleUpdated 
  } = useReplStore();

  useEffect(() => {
    const fetchExampleFile = async (path: string) => {
      const res = await fetch(`/examples/${path}`);
      return res.text();
    };

    const loadExamples = async () => {
      try {
        const examplesList = [
          {
            title: 'Hello world',
            files: [
              {
                path: 'Main.java',
                content: await fetchExampleFile('hello-world/Main.java')
              }
            ]
          },
          {
            title: 'GUI with Swing',
            files: [
              {
                path: 'Main.java',
                content: await fetchExampleFile('hello-world-swing/Main.java')
              }
            ]
          }
        ];
        setExamples(examplesList);
      } catch (error) {
        console.error('Failed to load examples:', error);
        // Set default examples if fetch fails
        setExamples([
          {
            title: 'Hello world',
            files: [
              {
                path: 'Main.java',
                content: `package fiddle;

class Main {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}`
              }
            ]
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadExamples();
  }, []);

  const openExample = (example: Fiddle) => {
    if (favouriteIndex !== -1) {
      setFavouriteIndex(-1);
    }
    setFiles(example.files);
    setFiddleTitle(example.title);
    setFiddleUpdated(example.updated);
    
    // Update URL with fragment
    const fiddleFragmentURL = compress(example);
    router.push(`/#${fiddleFragmentURL}`);
  };

  if (loading) {
    return (
      <div className="flex justify-center grow">
        <Loading />
      </div>
    );
  }

  return (
    <>
      {examples.map((fiddle, index) => (
        <button
          key={index}
          className="w-full text-left flex items-center px-4 py-2 hover:bg-stone-200 dark:hover:bg-stone-900"
          onClick={() => openExample(fiddle)}
        >
          <div className="grow">{fiddle.title}</div>
        </button>
      ))}
    </>
  );
}
