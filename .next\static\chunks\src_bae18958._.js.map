{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/lib/stores/repl-store.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\nimport type { Fiddle } from '../compress-fiddle';\n\nexport type File = {\n  path: string;\n  content: string;\n};\n\ninterface ReplState {\n  // File management\n  files: File[];\n  selectedFilePath: string;\n  setFiles: (files: File[]) => void;\n  setSelectedFilePath: (path: string) => void;\n  \n  // Fiddle metadata\n  fiddleTitle: string;\n  fiddleUpdated: Date | undefined;\n  setFiddleTitle: (title: string) => void;\n  setFiddleUpdated: (date: Date | undefined) => void;\n  \n  // Favorites\n  favourites: Fiddle[];\n  favouriteIndex: number;\n  setFavourites: (favourites: Fiddle[]) => void;\n  setFavouriteIndex: (index: number) => void;\n  \n  // UI state\n  isSidebarOpen: boolean;\n  setIsSidebarOpen: (open: boolean) => void;\n  \n  // Execution state\n  autoRun: boolean;\n  isRunning: boolean;\n  isSaved: boolean;\n  runCode: boolean;\n  compileLog: string;\n  setAutoRun: (autoRun: boolean) => void;\n  setIsRunning: (running: boolean) => void;\n  setIsSaved: (saved: boolean) => void;\n  setRunCode: (run: boolean) => void;\n  setCompileLog: (log: string) => void;\n}\n\n// Create the main repl store\nexport const useReplStore = create<ReplState>()(\n  persist(\n    (set, get) => ({\n      // File management\n      files: [],\n      selectedFilePath: 'Main.java',\n      setFiles: (files) => set({ files }),\n      setSelectedFilePath: (selectedFilePath) => set({ selectedFilePath }),\n      \n      // Fiddle metadata\n      fiddleTitle: '',\n      fiddleUpdated: undefined,\n      setFiddleTitle: (fiddleTitle) => set({ fiddleTitle }),\n      setFiddleUpdated: (fiddleUpdated) => set({ fiddleUpdated }),\n      \n      // Favorites\n      favourites: [],\n      favouriteIndex: -1,\n      setFavourites: (favourites) => set({ favourites }),\n      setFavouriteIndex: (favouriteIndex) => set({ favouriteIndex }),\n      \n      // UI state\n      isSidebarOpen: true,\n      setIsSidebarOpen: (isSidebarOpen) => set({ isSidebarOpen }),\n      \n      // Execution state\n      autoRun: false,\n      isRunning: false,\n      isSaved: true,\n      runCode: false,\n      compileLog: '',\n      setAutoRun: (autoRun) => set({ autoRun }),\n      setIsRunning: (isRunning) => set({ isRunning }),\n      setIsSaved: (isSaved) => set({ isSaved }),\n      setRunCode: (runCode) => set({ runCode }),\n      setCompileLog: (compileLog) => set({ compileLog }),\n    }),\n    {\n      name: 'javafiddle-repl-storage',\n      storage: createJSONStorage(() => localStorage),\n      // Only persist certain values\n      partialize: (state) => ({\n        isSidebarOpen: state.isSidebarOpen,\n        autoRun: state.autoRun,\n      }),\n    }\n  )\n);\n\n// Create a separate store for favorites that uses IndexedDB-like storage\nexport const useFavouritesStore = create<{\n  favourites: Fiddle[];\n  setFavourites: (favourites: Fiddle[]) => void;\n  addFavourite: (fiddle: Fiddle) => void;\n  removeFavourite: (index: number) => void;\n}>()(\n  persist(\n    (set, get) => ({\n      favourites: [],\n      setFavourites: (favourites) => set({ favourites }),\n      addFavourite: (fiddle) => set((state) => ({ \n        favourites: [...state.favourites, fiddle] \n      })),\n      removeFavourite: (index) => set((state) => ({ \n        favourites: state.favourites.filter((_, i) => i !== index) \n      })),\n    }),\n    {\n      name: 'javafiddle-favourites-storage',\n      storage: createJSONStorage(() => localStorage),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AA6CO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,kBAAkB;QAClB,OAAO,EAAE;QACT,kBAAkB;QAClB,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,qBAAqB,CAAC,mBAAqB,IAAI;gBAAE;YAAiB;QAElE,kBAAkB;QAClB,aAAa;QACb,eAAe;QACf,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QACnD,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QAEzD,YAAY;QACZ,YAAY,EAAE;QACd,gBAAgB,CAAC;QACjB,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;QAChD,mBAAmB,CAAC,iBAAmB,IAAI;gBAAE;YAAe;QAE5D,WAAW;QACX,eAAe;QACf,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QAEzD,kBAAkB;QAClB,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,YAAY;QACZ,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,cAAc,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC7C,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QACvC,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;IAClD,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,8BAA8B;IAC9B,YAAY,CAAC,QAAU,CAAC;YACtB,eAAe,MAAM,aAAa;YAClC,SAAS,MAAM,OAAO;QACxB,CAAC;AACH;AAKG,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAMrC,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,YAAY,EAAE;QACd,eAAe,CAAC,aAAe,IAAI;gBAAE;YAAW;QAChD,cAAc,CAAC,SAAW,IAAI,CAAC,QAAU,CAAC;oBACxC,YAAY;2BAAI,MAAM,UAAU;wBAAE;qBAAO;gBAC3C,CAAC;QACD,iBAAiB,CAAC,QAAU,IAAI,CAAC,QAAU,CAAC;oBAC1C,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBACtD,CAAC;IACH,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;AACnC", "debugId": null}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/lib/compress-fiddle.ts"], "sourcesContent": ["import lz from 'lz-string';\nimport z from 'zod';\n\nconst fiddle = z.object({\n  files: z.array(\n    z.object({\n      path: z.string(),\n      content: z.string()\n    })\n  ),\n  title: z.string(),\n  updated: z.coerce.date().optional()\n});\n\nexport type Fiddle = z.infer<typeof fiddle>;\n\nexport function compress(data: Fiddle): string {\n  return lz.compressToEncodedURIComponent(JSON.stringify(data));\n}\n\nexport function decompress(str: string): Fiddle {\n  return fiddle.parse(JSON.parse(lz.decompressFromEncodedURIComponent(str)));\n}\n\nexport const defaultFiddle: Fiddle = {\n  title: '',\n  files: [\n    {\n      path: 'Main.java',\n      content: `package fiddle;\n\nclass Main {\n\tpublic static void main(String[] args) {\n\t\tSystem.out.println(\"Hello, World!\");\n\t}\n}\n`\n    }\n  ]\n};\n\nexport const defaultFiddleComp = compress(defaultFiddle);\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEA,MAAM,SAAS,+IAAA,CAAA,UAAC,CAAC,MAAM,CAAC;IACtB,OAAO,+IAAA,CAAA,UAAC,CAAC,KAAK,CACZ,+IAAA,CAAA,UAAC,CAAC,MAAM,CAAC;QACP,MAAM,+IAAA,CAAA,UAAC,CAAC,MAAM;QACd,SAAS,+IAAA,CAAA,UAAC,CAAC,MAAM;IACnB;IAEF,OAAO,+IAAA,CAAA,UAAC,CAAC,MAAM;IACf,SAAS,+IAAA,CAAA,UAAC,CAAC,MAAM,CAAC,IAAI,GAAG,QAAQ;AACnC;AAIO,SAAS,SAAS,IAAY;IACnC,OAAO,uJAAA,CAAA,UAAE,CAAC,6BAA6B,CAAC,KAAK,SAAS,CAAC;AACzD;AAEO,SAAS,WAAW,GAAW;IACpC,OAAO,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,uJAAA,CAAA,UAAE,CAAC,iCAAiC,CAAC;AACtE;AAEO,MAAM,gBAAwB;IACnC,OAAO;IACP,OAAO;QACL;YACE,MAAM;YACN,SAAU;QAQZ;KACD;AACH;AAEO,MAAM,oBAAoB,SAAS", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/app-layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport { useReplStore } from \"@/lib/stores/repl-store\";\nimport {\n  decompress,\n  type Fiddle,\n  defaultFiddle,\n  defaultFiddleComp,\n} from \"@/lib/compress-fiddle\";\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function AppLayout({ children }: AppLayoutProps) {\n  const router = useRouter();\n  const { fiddleTitle, setFiles, setFiddleTitle, setFiddleUpdated } =\n    useReplStore();\n\n  useEffect(() => {\n    function setFiddle() {\n      const fragmentURL: string = window.location.hash.slice(1);\n      let fiddle: Fiddle;\n\n      if (!fragmentURL) {\n        fiddle = defaultFiddle;\n        router.replace(`/#${defaultFiddleComp}`);\n      } else {\n        try {\n          fiddle = decompress(fragmentURL);\n        } catch (error) {\n          console.error(\"Error decompressing fiddle:\", error);\n          fiddle = defaultFiddle;\n          router.replace(`/#${defaultFiddleComp}`);\n        }\n      }\n\n      setFiles(fiddle.files);\n      setFiddleTitle(fiddle.title);\n      setFiddleUpdated(fiddle.updated);\n    }\n\n    // Set initial fiddle\n    setFiddle();\n\n    // Listen for hash changes\n    const handlePopState = () => setFiddle();\n    window.addEventListener(\"popstate\", handlePopState);\n\n    return () => {\n      window.removeEventListener(\"popstate\", handlePopState);\n    };\n  }, [router, setFiles, setFiddleTitle, setFiddleUpdated]);\n\n  // Update document title dynamically\n  useEffect(() => {\n    const title = fiddleTitle\n      ? `${fiddleTitle} - JavaFiddle`\n      : \"JavaFiddle - Build and share Java code snippets in your browser\";\n    document.title = title;\n  }, [fiddleTitle]);\n\n  return (\n    <div className=\"bg-white text-black dark:bg-stone-900 dark:text-white\">\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAgBO,SAAS,UAAU,KAA4B;QAA5B,EAAE,QAAQ,EAAkB,GAA5B;;IACxB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,EAAE,GAC/D,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,SAAS;gBACP,MAAM,cAAsB,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;gBACvD,IAAI;gBAEJ,IAAI,CAAC,aAAa;oBAChB,SAAS,mIAAA,CAAA,gBAAa;oBACtB,OAAO,OAAO,CAAC,AAAC,KAAsB,OAAlB,mIAAA,CAAA,oBAAiB;gBACvC,OAAO;oBACL,IAAI;wBACF,SAAS,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD,EAAE;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+BAA+B;wBAC7C,SAAS,mIAAA,CAAA,gBAAa;wBACtB,OAAO,OAAO,CAAC,AAAC,KAAsB,OAAlB,mIAAA,CAAA,oBAAiB;oBACvC;gBACF;gBAEA,SAAS,OAAO,KAAK;gBACrB,eAAe,OAAO,KAAK;gBAC3B,iBAAiB,OAAO,OAAO;YACjC;YAEA,qBAAqB;YACrB;YAEA,0BAA0B;YAC1B,MAAM;sDAAiB,IAAM;;YAC7B,OAAO,gBAAgB,CAAC,YAAY;YAEpC;uCAAO;oBACL,OAAO,mBAAmB,CAAC,YAAY;gBACzC;;QACF;8BAAG;QAAC;QAAQ;QAAU;QAAgB;KAAiB;IAEvD,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,QAAQ,cACV,AAAC,GAAc,OAAZ,aAAY,mBACf;YACJ,SAAS,KAAK,GAAG;QACnB;8BAAG;QAAC;KAAY;IAEhB,qBACE,6LAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;GArDgB;;QACC,qIAAA,CAAA,YAAS;QAEtB,wIAAA,CAAA,eAAY;;;KAHA", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/menu/fiddle-title.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Icon } from '@iconify/react';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { compress } from '@/lib/compress-fiddle';\n\nexport function FiddleTitle() {\n  const [isEditing, setIsEditing] = useState(false);\n  const inputRef = useRef<HTMLInputElement>(null);\n  const router = useRouter();\n  \n  const { fiddleTitle, fiddleUpdated, files, setFiddleTitle } = useReplStore();\n\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  const handleBlur = () => {\n    setIsEditing(false);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      setIsEditing(false);\n      // Update URL since title changed\n      const fiddleFragmentURL = compress({\n        title: fiddleTitle,\n        updated: fiddleUpdated,\n        files: files\n      });\n      router.replace(`/#${fiddleFragmentURL}`);\n    }\n  };\n\n  const handleEditClick = () => {\n    setIsEditing(true);\n  };\n\n  if (isEditing) {\n    return (\n      <input\n        ref={inputRef}\n        type=\"text\"\n        value={fiddleTitle}\n        onChange={(e) => setFiddleTitle(e.target.value)}\n        onBlur={handleBlur}\n        onKeyDown={handleKeyDown}\n        className=\"text-lg block focus:outline-0 bg-inherit\"\n        autoComplete=\"off\"\n      />\n    );\n  }\n\n  return (\n    <h1 className=\"text-lg flex items-center gap-1.5\">\n      {fiddleTitle || 'Untitled'}\n      <button onClick={handleEditClick}>\n        <Icon icon=\"mi:edit\" />\n        <span className=\"sr-only\">Edit title</span>\n      </button>\n    </h1>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAa,SAAS,OAAO,EAAE;gBACjC,SAAS,OAAO,CAAC,KAAK;YACxB;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,aAAa;QACjB,aAAa;IACf;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,aAAa;YACb,iCAAiC;YACjC,MAAM,oBAAoB,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD,EAAE;gBACjC,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA,OAAO,OAAO,CAAC,AAAC,KAAsB,OAAlB;QACtB;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;IACf;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC;YACC,KAAK;YACL,MAAK;YACL,OAAO;YACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;YAC9C,QAAQ;YACR,WAAW;YACX,WAAU;YACV,cAAa;;;;;;IAGnB;IAEA,qBACE,6LAAC;QAAG,WAAU;;YACX,eAAe;0BAChB,6LAAC;gBAAO,SAAS;;kCACf,6LAAC,wJAAA,CAAA,OAAI;wBAAC,MAAK;;;;;;kCACX,6LAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAIlC;GA1DgB;;QAGC,qIAAA,CAAA,YAAS;QAEsC,wIAAA,CAAA,eAAY;;;KAL5D", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/settings/theme-switcher.tsx"], "sourcesContent": ["'use client';\n\nimport { Icon } from '@iconify/react';\nimport { useTheme } from 'next-themes';\n\nexport function ThemeSwitcher() {\n  const { theme, setTheme } = useTheme();\n\n  const toggle = () => {\n    setTheme(theme === 'dark' ? 'light' : 'dark');\n  };\n\n  return (\n    <button\n      className=\"flex items-center gap-1 w-full px-2 py-1 bg-white dark:bg-stone-800 border border-stone-500 rounded\"\n      onClick={toggle}\n      aria-label=\"Toggle dark theme\"\n    >\n      <Icon \n        icon={theme === 'dark' ? 'mi:moon' : 'mi:sun'} \n        className=\"w-5 h-5\" \n      />\n      {theme === 'dark' ? 'Dark' : 'Light'}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,SAAS;QACb,SAAS,UAAU,SAAS,UAAU;IACxC;IAEA,qBACE,6LAAC;QACC,WAAU;QACV,SAAS;QACT,cAAW;;0BAEX,6LAAC,wJAAA,CAAA,OAAI;gBACH,MAAM,UAAU,SAAS,YAAY;gBACrC,WAAU;;;;;;YAEX,UAAU,SAAS,SAAS;;;;;;;AAGnC;GApBgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/settings/settings-panel.tsx"], "sourcesContent": ["'use client';\n\nimport { ThemeSwitcher } from './theme-switcher';\nimport { useReplStore } from '@/lib/stores/repl-store';\n\nexport function SettingsPanel() {\n  const { autoRun, isRunning, setAutoRun, setRunCode } = useReplStore();\n\n  const handleAutoRunChange = (checked: boolean) => {\n    setAutoRun(checked);\n    // If autorun is set, force re-run by updating runCode\n    if (checked && !isRunning) {\n      setRunCode(true);\n    }\n  };\n\n  return (\n    <>\n      {/* Triangle pointing above */}\n      <svg\n        width=\"16\"\n        height=\"8\"\n        viewBox=\"0 0 20 10\"\n        className=\"ml-auto mr-[44px]\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <path d=\"M0 10L10 0L20 10H0Z\" className=\"fill-stone-200 dark:fill-stone-950\" />\n      </svg>\n\n      <div className=\"bg-stone-200 dark:bg-stone-950 w-[300px] max-w-full rounded-md shadow-sm px-4 py-3 text-sm accent-orange-600 dark:accent-orange-400 leading-relaxed\">\n        <h3 className=\"font-semibold\">Appearance</h3>\n        <div className=\"w-1/3\">\n          Theme:\n          <ThemeSwitcher />\n        </div>\n\n        <div className=\"border-t border-stone-300 dark:border-stone-700 my-3\" />\n\n        <h3 className=\"font-semibold\">Behaviour</h3>\n        <div className=\"flex items-center gap-1.5\">\n          <input \n            type=\"checkbox\" \n            checked={autoRun}\n            onChange={(e) => handleAutoRunChange(e.target.checked)}\n            id=\"auto-run\" \n          />\n          <label htmlFor=\"auto-run\" className=\"grow\">Run code automatically</label>\n        </div>\n      </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKO,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAElE,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,sDAAsD;QACtD,IAAI,WAAW,CAAC,WAAW;YACzB,WAAW;QACb;IACF;IAEA,qBACE;;0BAEE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,WAAU;gBACV,OAAM;0BAEN,cAAA,6LAAC;oBAAK,GAAE;oBAAsB,WAAU;;;;;;;;;;;0BAG1C,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,6LAAC;wBAAI,WAAU;;4BAAQ;0CAErB,6LAAC,sJAAA,CAAA,gBAAa;;;;;;;;;;;kCAGhB,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAG,WAAU;kCAAgB;;;;;;kCAC9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,OAAO;gCACrD,IAAG;;;;;;0CAEL,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;AAKrD;GA9CgB;;QACyC,wIAAA,CAAA,eAAY;;;KADrD", "debugId": null}}, {"offset": {"line": 555, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/menu/settings-button.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { Icon } from '@iconify/react';\nimport { SettingsPanel } from '@/components/settings/settings-panel';\n\nexport function SettingsButton() {\n  const [isOpen, setIsOpen] = useState(false);\n  const panelRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('click', handleClickOutside);\n      return () => document.removeEventListener('click', handleClickOutside);\n    }\n  }, [isOpen]);\n\n  const handleClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    setIsOpen(true);\n  };\n\n  const handlePanelClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n  };\n\n  return (\n    <>\n      <button\n        onClick={handleClick}\n        className=\"text-sm rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-3 h-8 flex items-center gap-1\"\n      >\n        <Icon icon=\"mi:filter\" className=\"w-5 h-5\" />\n        Settings\n      </button>\n\n      {isOpen && (\n        <div\n          ref={panelRef}\n          className=\"absolute top-[52px] right-4 z-10\"\n          onClick={handlePanelClick}\n        >\n          <SettingsPanel />\n        </div>\n      )}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;+DAAqB,CAAC;oBAC1B,IAAI,SAAS,OAAO,IAAI,CAAC,SAAS,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBACxE,UAAU;oBACZ;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,SAAS;gBACnC;gDAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;;YACrD;QACF;mCAAG;QAAC;KAAO;IAEX,MAAM,cAAc,CAAC;QACnB,EAAE,eAAe;QACjB,UAAU;IACZ;IAEA,MAAM,mBAAmB,CAAC;QACxB,EAAE,eAAe;IACnB;IAEA,qBACE;;0BACE,6LAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,6LAAC,wJAAA,CAAA,OAAI;wBAAC,MAAK;wBAAY,WAAU;;;;;;oBAAY;;;;;;;YAI9C,wBACC,6LAAC;gBACC,KAAK;gBACL,WAAU;gBACV,SAAS;0BAET,cAAA,6LAAC,sJAAA,CAAA,gBAAa;;;;;;;;;;;;AAKxB;GA/CgB;KAAA", "debugId": null}}, {"offset": {"line": 649, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/menu/favourite-button.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { useFavouritesStore } from '@/lib/stores/repl-store';\n\nexport function FavouriteButton() {\n  const { \n    fiddleTitle, \n    fiddleUpdated, \n    files, \n    favouriteIndex, \n    setFavouriteIndex \n  } = useReplStore();\n  \n  const { favourites, setFavourites } = useFavouritesStore();\n  \n  const isFavourite = favouriteIndex !== -1;\n\n  const toggle = () => {\n    if (isFavourite) {\n      const newFavourites = favourites.filter((_fiddle, i) => i !== favouriteIndex);\n      setFavourites(newFavourites);\n      setFavouriteIndex(-1);\n    } else {\n      const newFavourite = {\n        title: fiddleTitle,\n        updated: fiddleUpdated,\n        files: files\n      };\n      const newFavourites = [newFavourite, ...favourites];\n      setFavourites(newFavourites);\n      setFavouriteIndex(0);\n    }\n  };\n\n  // Keep favourites consistent with fiddle data\n  useEffect(() => {\n    if (isFavourite && favourites[favouriteIndex]) {\n      const updatedFavourites = [...favourites];\n      updatedFavourites[favouriteIndex] = {\n        title: fiddleTitle,\n        updated: fiddleUpdated,\n        files: files\n      };\n      setFavourites(updatedFavourites);\n    }\n  }, [fiddleTitle, fiddleUpdated, files, isFavourite, favouriteIndex, favourites, setFavourites]);\n\n  return (\n    <button \n      onClick={toggle} \n      className={`${isFavourite ? 'text-red-600 dark:text-red-400' : ''}`}\n    >\n      <Icon icon=\"mi:heart\" className=\"w-6 h-6\" />\n      <span className=\"sr-only\">\n        {isFavourite ? 'Remove from favorites' : 'Add to favorites'}\n      </span>\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;;AAOO,SAAS;;IACd,MAAM,EACJ,WAAW,EACX,aAAa,EACb,KAAK,EACL,cAAc,EACd,iBAAiB,EAClB,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEf,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD;IAEvD,MAAM,cAAc,mBAAmB,CAAC;IAExC,MAAM,SAAS;QACb,IAAI,aAAa;YACf,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAC,SAAS,IAAM,MAAM;YAC9D,cAAc;YACd,kBAAkB,CAAC;QACrB,OAAO;YACL,MAAM,eAAe;gBACnB,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA,MAAM,gBAAgB;gBAAC;mBAAiB;aAAW;YACnD,cAAc;YACd,kBAAkB;QACpB;IACF;IAEA,8CAA8C;IAC9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,eAAe,UAAU,CAAC,eAAe,EAAE;gBAC7C,MAAM,oBAAoB;uBAAI;iBAAW;gBACzC,iBAAiB,CAAC,eAAe,GAAG;oBAClC,OAAO;oBACP,SAAS;oBACT,OAAO;gBACT;gBACA,cAAc;YAChB;QACF;oCAAG;QAAC;QAAa;QAAe;QAAO;QAAa;QAAgB;QAAY;KAAc;IAE9F,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,AAAC,GAAsD,OAApD,cAAc,mCAAmC;;0BAE/D,6LAAC,wJAAA,CAAA,OAAI;gBAAC,MAAK;gBAAW,WAAU;;;;;;0BAChC,6LAAC;gBAAK,WAAU;0BACb,cAAc,0BAA0B;;;;;;;;;;;;AAIjD;GAtDgB;;QAOV,wIAAA,CAAA,eAAY;QAEsB,wIAAA,CAAA,qBAAkB;;;KAT1C", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/menu.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { Icon } from '@iconify/react';\nimport TimeAgo from 'react-timeago';\nimport { FiddleTitle } from './menu/fiddle-title';\nimport { SettingsButton } from './menu/settings-button';\nimport { FavouriteButton } from './menu/favourite-button';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { defaultFiddle, defaultFiddleComp } from '@/lib/compress-fiddle';\n\ninterface MenuProps {\n  onShare: () => void;\n  onRun: () => void;\n}\n\nexport function Menu({ onShare, onRun }: MenuProps) {\n  const [showShareMessage, setShowShareMessage] = useState(false);\n  const router = useRouter();\n  \n  const {\n    files,\n    fiddleTitle,\n    fiddleUpdated,\n    favouriteIndex,\n    autoRun,\n    setFiles,\n    setFiddleTitle,\n    setFiddleUpdated,\n    setFavouriteIndex,\n  } = useReplStore();\n\n  // Handle share message timeout\n  useEffect(() => {\n    if (showShareMessage) {\n      const timeoutId = setTimeout(() => {\n        setShowShareMessage(false);\n      }, 800);\n      return () => clearTimeout(timeoutId);\n    }\n  }, [showShareMessage]);\n\n  // Handle keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      if ((e.ctrlKey || e.metaKey) && e.key === 's') {\n        e.preventDefault();\n        onShare();\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyDown);\n    return () => window.removeEventListener('keydown', handleKeyDown);\n  }, [onShare]);\n\n  const createNewFile = () => {\n    setFiles(defaultFiddle.files);\n    setFiddleTitle(defaultFiddle.title);\n    setFiddleUpdated(defaultFiddle.updated);\n    if (favouriteIndex !== -1) {\n      setFavouriteIndex(-1);\n    }\n    router.push(`/#${defaultFiddleComp}`);\n  };\n\n  const handleShare = () => {\n    onShare();\n    setShowShareMessage(true);\n  };\n\n  return (\n    <header className=\"px-4 h-16 flex items-center justify-between gap-4 relative shadow dark:shadow-none dark:border-b border-b-stone-700 dark:bg-stone-800\">\n      <button \n        className=\"text-xl text-orange-500 dark:text-orange-400 font-bold\" \n        onClick={createNewFile}\n      >\n        <h1>JavaFiddle</h1>\n      </button>\n\n      <div className=\"grow flex flex-col justify-center self-stretch\">\n        <FiddleTitle />\n        {fiddleUpdated && (\n          <div className=\"h-4 leading-3 text-xs text-stone-500 dark:text-stone-400\">\n            <TimeAgo date={fiddleUpdated} />\n          </div>\n        )}\n      </div>\n\n      <ul className=\"flex items-center gap-2\">\n        {showShareMessage && (\n          <li className=\"text-xs text-stone-600 dark:text-stone-400\">\n            URL copied to clipboard\n          </li>\n        )}\n        <FavouriteButton />\n        {!autoRun && (\n          <li>\n            <button\n              onClick={onRun}\n              className=\"text-sm flex items-center rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-2 py-1 h-8\"\n            >\n              <Icon icon=\"mi:play\" className=\"w-5 h-5 mr-1\" />\n              Run\n            </button>\n          </li>\n        )}\n        <li>\n          <button\n            onClick={handleShare}\n            className=\"text-sm flex items-center rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-2 py-1 h-8\"\n            title=\"⌘S / Ctrl+S\"\n          >\n            <Icon icon=\"mi:share\" className=\"w-5 h-5 mr-1\" />\n            Share\n          </button>\n        </li>\n        <li>\n          <SettingsButton />\n        </li>\n      </ul>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAiBO,SAAS,KAAK,KAA6B;QAA7B,EAAE,OAAO,EAAE,KAAK,EAAa,GAA7B;;IACnB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,KAAK,EACL,WAAW,EACX,aAAa,EACb,cAAc,EACd,OAAO,EACP,QAAQ,EACR,cAAc,EACd,gBAAgB,EAChB,iBAAiB,EAClB,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEf,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,kBAAkB;gBACpB,MAAM,YAAY;gDAAW;wBAC3B,oBAAoB;oBACtB;+CAAG;gBACH;sCAAO,IAAM,aAAa;;YAC5B;QACF;yBAAG;QAAC;KAAiB;IAErB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;gDAAgB,CAAC;oBACrB,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;wBAC7C,EAAE,cAAc;wBAChB;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;yBAAG;QAAC;KAAQ;IAEZ,MAAM,gBAAgB;QACpB,SAAS,mIAAA,CAAA,gBAAa,CAAC,KAAK;QAC5B,eAAe,mIAAA,CAAA,gBAAa,CAAC,KAAK;QAClC,iBAAiB,mIAAA,CAAA,gBAAa,CAAC,OAAO;QACtC,IAAI,mBAAmB,CAAC,GAAG;YACzB,kBAAkB,CAAC;QACrB;QACA,OAAO,IAAI,CAAC,AAAC,KAAsB,OAAlB,mIAAA,CAAA,oBAAiB;IACpC;IAEA,MAAM,cAAc;QAClB;QACA,oBAAoB;IACtB;IAEA,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBACC,WAAU;gBACV,SAAS;0BAET,cAAA,6LAAC;8BAAG;;;;;;;;;;;0BAGN,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,wJAAA,CAAA,cAAW;;;;;oBACX,+BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,mJAAA,CAAA,UAAO;4BAAC,MAAM;;;;;;;;;;;;;;;;;0BAKrB,6LAAC;gBAAG,WAAU;;oBACX,kCACC,6LAAC;wBAAG,WAAU;kCAA6C;;;;;;kCAI7D,6LAAC,4JAAA,CAAA,kBAAe;;;;;oBACf,CAAC,yBACA,6LAAC;kCACC,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC,wJAAA,CAAA,OAAI;oCAAC,MAAK;oCAAU,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAKtD,6LAAC;kCACC,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;;8CAEN,6LAAC,wJAAA,CAAA,OAAI;oCAAC,MAAK;oCAAW,WAAU;;;;;;gCAAiB;;;;;;;;;;;;kCAIrD,6LAAC;kCACC,cAAA,6LAAC,2JAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;;AAKzB;GA1GgB;;QAEC,qIAAA,CAAA,YAAS;QAYpB,wIAAA,CAAA,eAAY;;;KAdF", "debugId": null}}, {"offset": {"line": 991, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/sidebar/favourites.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport TimeAgo from 'react-timeago';\nimport { compress, type Fiddle } from '@/lib/compress-fiddle';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { useFavouritesStore } from '@/lib/stores/repl-store';\n\nexport function Favourites() {\n  const router = useRouter();\n  const { \n    favouriteIndex, \n    setFavouriteIndex, \n    setFiles, \n    setFiddleTitle, \n    setFiddleUpdated \n  } = useReplStore();\n  const { favourites } = useFavouritesStore();\n\n  const openFavourite = (favourite: Fiddle, index: number) => {\n    if (index === favouriteIndex) {\n      return;\n    }\n    \n    setFavouriteIndex(index);\n    setFiles(favourite.files);\n    setFiddleTitle(favourite.title);\n    setFiddleUpdated(favourite.updated);\n    \n    // Update URL with fragment\n    const fiddleFragmentURL = compress(favourite);\n    router.push(`/#${fiddleFragmentURL}`);\n  };\n\n  return (\n    <>\n      <p className=\"text-sm p-3 text-stone-500 dark:text-stone-400 leading-tight\">\n        Click the heart icon on a fiddle to have it appear in this list.\n      </p>\n\n      <ul className=\"text-stone-600 dark:text-stone-200 text-sm\">\n        {favourites.map((fiddle, index) => (\n          <li key={index}>\n            <button\n              className=\"w-full text-left flex items-center px-4 py-2 hover:bg-stone-200 dark:hover:bg-stone-900\"\n              onClick={() => openFavourite(fiddle, index)}\n            >\n              <div className=\"grow\">{fiddle.title || 'Untitled'}</div>\n              {fiddle.updated && (\n                <div className=\"text-xs opacity-50\">\n                  <TimeAgo date={fiddle.updated} />\n                </div>\n              )}\n            </button>\n          </li>\n        ))}\n      </ul>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;;AAQO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EACJ,cAAc,EACd,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,gBAAgB,EACjB,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IACf,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,qBAAkB,AAAD;IAExC,MAAM,gBAAgB,CAAC,WAAmB;QACxC,IAAI,UAAU,gBAAgB;YAC5B;QACF;QAEA,kBAAkB;QAClB,SAAS,UAAU,KAAK;QACxB,eAAe,UAAU,KAAK;QAC9B,iBAAiB,UAAU,OAAO;QAElC,2BAA2B;QAC3B,MAAM,oBAAoB,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,OAAO,IAAI,CAAC,AAAC,KAAsB,OAAlB;IACnB;IAEA,qBACE;;0BACE,6LAAC;gBAAE,WAAU;0BAA+D;;;;;;0BAI5E,6LAAC;gBAAG,WAAU;0BACX,WAAW,GAAG,CAAC,CAAC,QAAQ,sBACvB,6LAAC;kCACC,cAAA,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,cAAc,QAAQ;;8CAErC,6LAAC;oCAAI,WAAU;8CAAQ,OAAO,KAAK,IAAI;;;;;;gCACtC,OAAO,OAAO,kBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mJAAA,CAAA,UAAO;wCAAC,MAAM,OAAO,OAAO;;;;;;;;;;;;;;;;;uBAR5B;;;;;;;;;;;;AAiBnB;GAnDgB;;QACC,qIAAA,CAAA,YAAS;QAOpB,wIAAA,CAAA,eAAY;QACO,wIAAA,CAAA,qBAAkB;;;KAT3B", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/loading.tsx"], "sourcesContent": ["'use client';\n\nimport { useTheme } from 'next-themes';\n\nexport function Loading() {\n  const { theme } = useTheme();\n  \n  const spinner = theme === 'dark' \n    ? '/assets/loading-spinner-white.svg' \n    : '/assets/loading-spinner.svg';\n\n  return (\n    <div className=\"w-full h-full flex items-center justify-center\">\n      <img src={spinner} className=\"w-10 h-10\" alt=\"Loading\" />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEzB,MAAM,UAAU,UAAU,SACtB,sCACA;IAEJ,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,KAAK;YAAS,WAAU;YAAY,KAAI;;;;;;;;;;;AAGnD;GAZgB;;QACI,mJAAA,CAAA,WAAQ;;;KADZ", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/sidebar/examples.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { compress, type Fiddle } from '@/lib/compress-fiddle';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { Loading } from '@/components/loading';\n\nexport function Examples() {\n  const [examples, setExamples] = useState<Fiddle[]>([]);\n  const [loading, setLoading] = useState(true);\n  const router = useRouter();\n  \n  const { \n    favouriteIndex, \n    setFavouriteIndex, \n    setFiles, \n    setFiddleTitle, \n    setFiddleUpdated \n  } = useReplStore();\n\n  useEffect(() => {\n    const fetchExampleFile = async (path: string) => {\n      const res = await fetch(`/examples/${path}`);\n      return res.text();\n    };\n\n    const loadExamples = async () => {\n      try {\n        const examplesList = [\n          {\n            title: 'Hello world',\n            files: [\n              {\n                path: 'Main.java',\n                content: await fetchExampleFile('hello-world/Main.java')\n              }\n            ]\n          },\n          {\n            title: 'GUI with Swing',\n            files: [\n              {\n                path: 'Main.java',\n                content: await fetchExampleFile('hello-world-swing/Main.java')\n              }\n            ]\n          }\n        ];\n        setExamples(examplesList);\n      } catch (error) {\n        console.error('Failed to load examples:', error);\n        // Set default examples if fetch fails\n        setExamples([\n          {\n            title: 'Hello world',\n            files: [\n              {\n                path: 'Main.java',\n                content: `package fiddle;\n\nclass Main {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}`\n              }\n            ]\n          }\n        ]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadExamples();\n  }, []);\n\n  const openExample = (example: Fiddle) => {\n    if (favouriteIndex !== -1) {\n      setFavouriteIndex(-1);\n    }\n    setFiles(example.files);\n    setFiddleTitle(example.title);\n    setFiddleUpdated(example.updated);\n    \n    // Update URL with fragment\n    const fiddleFragmentURL = compress(example);\n    router.push(`/#${fiddleFragmentURL}`);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center grow\">\n        <Loading />\n      </div>\n    );\n  }\n\n  return (\n    <>\n      {examples.map((fiddle, index) => (\n        <button\n          key={index}\n          className=\"w-full text-left flex items-center px-4 py-2 hover:bg-stone-200 dark:hover:bg-stone-900\"\n          onClick={() => openExample(fiddle)}\n        >\n          <div className=\"grow\">{fiddle.title}</div>\n        </button>\n      ))}\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,cAAc,EACd,iBAAiB,EACjB,QAAQ,EACR,cAAc,EACd,gBAAgB,EACjB,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM;uDAAmB,OAAO;oBAC9B,MAAM,MAAM,MAAM,MAAM,AAAC,aAAiB,OAAL;oBACrC,OAAO,IAAI,IAAI;gBACjB;;YAEA,MAAM;mDAAe;oBACnB,IAAI;wBACF,MAAM,eAAe;4BACnB;gCACE,OAAO;gCACP,OAAO;oCACL;wCACE,MAAM;wCACN,SAAS,MAAM,iBAAiB;oCAClC;iCACD;4BACH;4BACA;gCACE,OAAO;gCACP,OAAO;oCACL;wCACE,MAAM;wCACN,SAAS,MAAM,iBAAiB;oCAClC;iCACD;4BACH;yBACD;wBACD,YAAY;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,sCAAsC;wBACtC,YAAY;4BACV;gCACE,OAAO;gCACP,OAAO;oCACL;wCACE,MAAM;wCACN,SAAU;oCAOZ;iCACD;4BACH;yBACD;oBACH,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;6BAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,IAAI,mBAAmB,CAAC,GAAG;YACzB,kBAAkB,CAAC;QACrB;QACA,SAAS,QAAQ,KAAK;QACtB,eAAe,QAAQ,KAAK;QAC5B,iBAAiB,QAAQ,OAAO;QAEhC,2BAA2B;QAC3B,MAAM,oBAAoB,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,OAAO,IAAI,CAAC,AAAC,KAAsB,OAAlB;IACnB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,gIAAA,CAAA,UAAO;;;;;;;;;;IAGd;IAEA,qBACE;kBACG,SAAS,GAAG,CAAC,CAAC,QAAQ,sBACrB,6LAAC;gBAEC,WAAU;gBACV,SAAS,IAAM,YAAY;0BAE3B,cAAA,6LAAC;oBAAI,WAAU;8BAAQ,OAAO,KAAK;;;;;;eAJ9B;;;;;;AASf;GAxGgB;;QAGC,qIAAA,CAAA,YAAS;QAQpB,wIAAA,CAAA,eAAY;;;KAXF", "debugId": null}}, {"offset": {"line": 1287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/sidebar/sidebar-options.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Icon } from '@iconify/react';\nimport { Favourites } from './favourites';\nimport { Examples } from './examples';\n\ninterface SidebarOptionsProps {\n  forceClose: boolean;\n  onSelectOption: () => void;\n}\n\nexport function SidebarOptions({ forceClose, onSelectOption }: SidebarOptionsProps) {\n  const [selectedOptionIndex, setSelectedOptionIndex] = useState(-1);\n\n  const options = [\n    {\n      label: typeof navigator !== 'undefined' && navigator.language === 'en-GB' ? 'Favourites' : 'Favorites',\n      icon: 'mi:heart',\n      color: 'red'\n    },\n    {\n      label: 'Examples',\n      icon: 'mi:archive',\n      color: 'teal'\n    }\n  ];\n\n  const selectedOption = selectedOptionIndex === -1 ? undefined : options[selectedOptionIndex];\n\n  useEffect(() => {\n    if (forceClose) {\n      setSelectedOptionIndex(-1);\n    }\n  }, [forceClose]);\n\n  useEffect(() => {\n    if (selectedOptionIndex !== -1) {\n      onSelectOption();\n    }\n  }, [selectedOptionIndex, onSelectOption]);\n\n  if (selectedOption) {\n    return (\n      <>\n        <div className=\"border-y dark:border-stone-700\">\n          <button\n            className=\"flex items-center gap-3 px-2.5 h-10 w-full font-medium\"\n            onClick={() => setSelectedOptionIndex(-1)}\n          >\n            <Icon icon=\"mi:chevron-left\" className=\"w-5 h-5\" />\n            {selectedOption.label}\n          </button>\n        </div>\n        {selectedOptionIndex === 0 && <Favourites />}\n        {selectedOptionIndex === 1 && <Examples />}\n      </>\n    );\n  }\n\n  return (\n    <ul>\n      {options.map((option, index) => (\n        <li key={option.label} className=\"border-b first:border-t dark:border-stone-700\">\n          <button\n            className={`group flex items-center gap-3 px-2.5 h-10 w-full text-stone-600 dark:text-stone-400 ${\n              option.color === 'red' \n                ? 'hover:text-red-600 dark:hover:text-red-400' \n                : 'hover:text-teal-600 dark:hover:text-teal-400'\n            }`}\n            onClick={() => setSelectedOptionIndex(index)}\n          >\n            <Icon icon={option.icon} className=\"w-5 h-5\" />\n            <span className=\"grow text-left font-medium\">{option.label}</span>\n            <Icon icon=\"mi:chevron-right\" className=\"opacity-0 group-hover:opacity-100\" />\n          </button>\n        </li>\n      ))}\n    </ul>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,eAAe,KAAmD;QAAnD,EAAE,UAAU,EAAE,cAAc,EAAuB,GAAnD;;IAC7B,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEhE,MAAM,UAAU;QACd;YACE,OAAO,OAAO,cAAc,eAAe,UAAU,QAAQ,KAAK,UAAU,eAAe;YAC3F,MAAM;YACN,OAAO;QACT;QACA;YACE,OAAO;YACP,MAAM;YACN,OAAO;QACT;KACD;IAED,MAAM,iBAAiB,wBAAwB,CAAC,IAAI,YAAY,OAAO,CAAC,oBAAoB;IAE5F,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,YAAY;gBACd,uBAAuB,CAAC;YAC1B;QACF;mCAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,wBAAwB,CAAC,GAAG;gBAC9B;YACF;QACF;mCAAG;QAAC;QAAqB;KAAe;IAExC,IAAI,gBAAgB;QAClB,qBACE;;8BACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,uBAAuB,CAAC;;0CAEvC,6LAAC,wJAAA,CAAA,OAAI;gCAAC,MAAK;gCAAkB,WAAU;;;;;;4BACtC,eAAe,KAAK;;;;;;;;;;;;gBAGxB,wBAAwB,mBAAK,6LAAC,sJAAA,CAAA,aAAU;;;;;gBACxC,wBAAwB,mBAAK,6LAAC,oJAAA,CAAA,WAAQ;;;;;;;IAG7C;IAEA,qBACE,6LAAC;kBACE,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;gBAAsB,WAAU;0BAC/B,cAAA,6LAAC;oBACC,WAAW,AAAC,uFAIX,OAHC,OAAO,KAAK,KAAK,QACb,+CACA;oBAEN,SAAS,IAAM,uBAAuB;;sCAEtC,6LAAC,wJAAA,CAAA,OAAI;4BAAC,MAAM,OAAO,IAAI;4BAAE,WAAU;;;;;;sCACnC,6LAAC;4BAAK,WAAU;sCAA8B,OAAO,KAAK;;;;;;sCAC1D,6LAAC,wJAAA,CAAA,OAAI;4BAAC,MAAK;4BAAmB,WAAU;;;;;;;;;;;;eAXnC,OAAO,KAAK;;;;;;;;;;AAiB7B;GApEgB;KAAA", "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { Icon } from '@iconify/react';\nimport { useTheme } from 'next-themes';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { SidebarOptions } from './sidebar/sidebar-options';\n\nexport function Sidebar() {\n  const { theme } = useTheme();\n  const { isSidebarOpen, setIsSidebarOpen } = useReplStore();\n\n  // Import logos dynamically based on theme\n  const cheerpjLogotypeUrl = theme === 'dark' \n    ? '/assets/cheerpj/logotype-white.svg' \n    : '/assets/cheerpj/logotype-grey.svg';\n\n  return (\n    <aside className={`w-10 bg-stone-100 dark:bg-stone-800 flex-shrink-0 transition-[width] flex flex-col overflow-hidden ${isSidebarOpen ? '!w-80' : ''}`}>\n      <div className={`text-right shadow-none animate-shadow ${isSidebarOpen ? '!shadow' : ''}`}>\n        <button\n          className=\"w-10 h-10 text-stone-600 hover:text-stone-900 dark:text-stone-400 dark:hover:text-stone-100 inline-flex items-center justify-center\"\n          onClick={() => setIsSidebarOpen(!isSidebarOpen)}\n        >\n          {isSidebarOpen ? (\n            <Icon icon=\"mi:close\" className=\"w-4 h-4\" />\n          ) : (\n            <Icon icon=\"mi:menu\" className=\"w-5 h-5\" />\n          )}\n        </button>\n      </div>\n\n      <div className=\"w-80 grow overflow-hidden\">\n        <div className=\"h-1/2 overflow-y-auto flex flex-col\">\n          <SidebarOptions\n            forceClose={!isSidebarOpen}\n            onSelectOption={() => setIsSidebarOpen(true)}\n          />\n        </div>\n        {isSidebarOpen && (\n          <div className=\"h-1/2 overflow-y-auto flex flex-col\">\n            <div className=\"grow p-4 leading-tight bg-stone-200 text-stone-700 dark:bg-stone-700 dark:text-stone-300 text-sm\">\n              <p>\n                JavaFiddle is an online tool to <b>build</b> and <b>share</b> snippets of Java code.\n              </p>\n\n              <hr className=\"my-6 border-stone-300 dark:border-stone-600\" />\n\n              <ul className=\"list-disc space-y-2 ml-3\">\n                <li>\n                  Runs entirely <b>in your browser</b>.\n                </li>\n                <li>Supports all of Java SE 8, including Swing.</li>\n              </ul>\n\n              <hr className=\"my-6 border-stone-300 dark:border-stone-600\" />\n\n              <p>\n                Powered by{' '}\n                <a\n                  href=\"https://leaningtech.com/cheerpj/\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"underline\"\n                >\n                  CheerpJ\n                </a>\n              </p>\n\n              <div className=\"mt-4\">\n                <img \n                  src={cheerpjLogotypeUrl} \n                  alt=\"CheerpJ\" \n                  className=\"h-6\" \n                />\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEvD,0CAA0C;IAC1C,MAAM,qBAAqB,UAAU,SACjC,uCACA;IAEJ,qBACE,6LAAC;QAAM,WAAW,AAAC,sGAAkI,OAA7B,gBAAgB,UAAU;;0BAChJ,6LAAC;gBAAI,WAAW,AAAC,yCAAuE,OAA/B,gBAAgB,YAAY;0BACnF,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS,IAAM,iBAAiB,CAAC;8BAEhC,8BACC,6LAAC,wJAAA,CAAA,OAAI;wBAAC,MAAK;wBAAW,WAAU;;;;;6CAEhC,6LAAC,wJAAA,CAAA,OAAI;wBAAC,MAAK;wBAAU,WAAU;;;;;;;;;;;;;;;;0BAKrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8JAAA,CAAA,iBAAc;4BACb,YAAY,CAAC;4BACb,gBAAgB,IAAM,iBAAiB;;;;;;;;;;;oBAG1C,+BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAE;sDAC+B,6LAAC;sDAAE;;;;;;wCAAS;sDAAK,6LAAC;sDAAE;;;;;;wCAAS;;;;;;;8CAG/D,6LAAC;oCAAG,WAAU;;;;;;8CAEd,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;;gDAAG;8DACY,6LAAC;8DAAE;;;;;;gDAAmB;;;;;;;sDAEtC,6LAAC;sDAAG;;;;;;;;;;;;8CAGN,6LAAC;oCAAG,WAAU;;;;;;8CAEd,6LAAC;;wCAAE;wCACU;sDACX,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B;GA1EgB;;QACI,mJAAA,CAAA,WAAQ;QACkB,wIAAA,CAAA,eAAY;;;KAF1C", "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/lib/repl/linter.ts"], "sourcesContent": ["import { linter, type Diagnostic } from '@codemirror/lint';\nimport { Compartment, Facet } from '@codemirror/state';\nimport type { File } from '@/lib/stores/repl-store';\n\nconst linterService = linter(\n  (view) => {\n    return view.state.facet(diagnostic).flat();\n  },\n  {\n    needsRefresh: (update) => update.startState.facet(diagnostic) !== update.state.facet(diagnostic)\n  }\n);\n\nexport const diagnostic = Facet.define<Diagnostic[]>({\n  enables: linterService\n});\n\nexport const compartment = new Compartment();\n\nexport function parseCompileLog(log: string, files: File[]): Diagnostic[][] {\n  const logLines = log.split('\\n');\n  const diagnostics: Diagnostic[][] = [];\n  const re = /^\\/str\\/([^:]+):(\\d+): ([^:]+): ([^:]+)$/;\n\n  const filePathToIndex = new Map<string, number>();\n  for (let i = 0; i < files.length; i++) {\n    filePathToIndex.set(files[i].path, i);\n    diagnostics.push([]);\n  }\n\n  for (let i = 0; i < logLines.length; i++) {\n    const line = logLines[i];\n    const groups = line.match(re);\n    if (!groups) continue;\n    const [, path, lineNoStr, severity, message] = groups;\n    const fileIndex = filePathToIndex.get(path);\n    if (typeof fileIndex !== 'number') continue;\n    const lineNo = parseInt(lineNoStr);\n\n    // Find index that the line starts in the source file\n    const sourceFile = files[fileIndex];\n    let pos = 0;\n    for (let linesLeft = lineNo; linesLeft > 1 && pos < sourceFile.content.length; pos++) {\n      if (sourceFile.content[pos] === '\\n') linesLeft--;\n    }\n\n    // Column; position of \"^\"\n    pos += logLines[i + 2].length - 1;\n\n    diagnostics[fileIndex].push({\n      from: pos,\n      to: pos + 1,\n      severity: severity === 'error' ? 'error' : 'warning',\n      message\n    });\n  }\n  return diagnostics;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGA,MAAM,gBAAgB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EACzB,CAAC;IACC,OAAO,KAAK,KAAK,CAAC,KAAK,CAAC,YAAY,IAAI;AAC1C,GACA;IACE,cAAc,CAAC,SAAW,OAAO,UAAU,CAAC,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC,KAAK,CAAC;AACvF;AAGK,MAAM,aAAa,yJAAA,CAAA,QAAK,CAAC,MAAM,CAAe;IACnD,SAAS;AACX;AAEO,MAAM,cAAc,IAAI,yJAAA,CAAA,cAAW;AAEnC,SAAS,gBAAgB,GAAW,EAAE,KAAa;IACxD,MAAM,WAAW,IAAI,KAAK,CAAC;IAC3B,MAAM,cAA8B,EAAE;IACtC,MAAM,KAAK;IAEX,MAAM,kBAAkB,IAAI;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,gBAAgB,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,EAAE;QACnC,YAAY,IAAI,CAAC,EAAE;IACrB;IAEA,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,MAAM,OAAO,QAAQ,CAAC,EAAE;QACxB,MAAM,SAAS,KAAK,KAAK,CAAC;QAC1B,IAAI,CAAC,QAAQ;QACb,MAAM,GAAG,MAAM,WAAW,UAAU,QAAQ,GAAG;QAC/C,MAAM,YAAY,gBAAgB,GAAG,CAAC;QACtC,IAAI,OAAO,cAAc,UAAU;QACnC,MAAM,SAAS,SAAS;QAExB,qDAAqD;QACrD,MAAM,aAAa,KAAK,CAAC,UAAU;QACnC,IAAI,MAAM;QACV,IAAK,IAAI,YAAY,QAAQ,YAAY,KAAK,MAAM,WAAW,OAAO,CAAC,MAAM,EAAE,MAAO;YACpF,IAAI,WAAW,OAAO,CAAC,IAAI,KAAK,MAAM;QACxC;QAEA,0BAA0B;QAC1B,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG;QAEhC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1B,MAAM;YACN,IAAI,MAAM;YACV,UAAU,aAAa,UAAU,UAAU;YAC3C;QACF;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1730, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useCallback } from 'react';\nimport { basicSetup } from 'codemirror';\nimport { EditorView, keymap } from '@codemirror/view';\nimport { Compartment, EditorState } from '@codemirror/state';\nimport { indentWithTab } from '@codemirror/commands';\nimport { indentUnit } from '@codemirror/language';\nimport { lintGutter } from '@codemirror/lint';\nimport { java } from '@codemirror/lang-java';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { compartment, diagnostic, parseCompileLog } from '@/lib/repl/linter';\nimport { useTheme } from 'next-themes';\nimport { coolGlow, tomorrow } from 'thememirror';\nimport { compress } from '@/lib/compress-fiddle';\nimport '@/lib/repl/codemirror.css';\n\nexport function Editor() {\n  const containerRef = useRef<HTMLDivElement>(null);\n  const editorViewRef = useRef<EditorView | undefined>();\n  const editorStatesRef = useRef<Map<string, EditorState>>(new Map());\n  const themeCompartmentRef = useRef(new Compartment());\n  const skipResetRef = useRef(false);\n  \n  const { theme } = useTheme();\n  const {\n    files,\n    selectedFilePath,\n    fiddleTitle,\n    fiddleUpdated,\n    compileLog,\n    setFiles,\n    setFiddleTitle,\n    setFiddleUpdated,\n  } = useReplStore();\n\n  const editorTheme = theme === 'dark' ? coolGlow : tomorrow;\n\n  const extensions = useCallback(() => [\n    basicSetup,\n    keymap.of([indentWithTab]),\n    indentUnit.of('    '),\n    lintGutter(),\n    compartment.of(diagnostic.of([])),\n    themeCompartmentRef.current.of(editorTheme)\n  ], [editorTheme]);\n\n  const updateFragmentURL = useCallback(() => {\n    const newUpdated = new Date();\n    setFiddleUpdated(newUpdated);\n    const fiddleFragmentURL = compress({\n      title: fiddleTitle,\n      updated: newUpdated,\n      files: files\n    });\n    // Use replace so history is not updated for every change\n    window.history.replaceState(null, '', `#${fiddleFragmentURL}`);\n  }, [fiddleTitle, files, setFiddleUpdated]);\n\n  // Reset editor states when files change\n  const resetEditorStates = useCallback(() => {\n    if (skipResetRef.current) return;\n\n    for (const file of files) {\n      let state = editorStatesRef.current.get(file.path);\n      if (state) {\n        // Update state to match filesystem\n        const existing = state.doc.toString();\n        if (file.content !== existing) {\n          const transaction = state.update({\n            changes: {\n              from: 0,\n              to: existing.length,\n              insert: file.content\n            }\n          });\n          state = transaction.state;\n        }\n      } else {\n        const extension = file.path.split('.').pop();\n        state = EditorState.create({\n          doc: file.content,\n          extensions: extension === 'java' ? [...extensions(), java()] : extensions()\n        });\n      }\n\n      editorStatesRef.current.set(file.path, state);\n      if (file.path === selectedFilePath) {\n        editorViewRef.current?.setState(state);\n      }\n    }\n  }, [files, selectedFilePath, extensions]);\n\n  // Initialize editor\n  useEffect(() => {\n    if (!containerRef.current) return;\n\n    const editorView = new EditorView({\n      parent: containerRef.current,\n      state: editorStatesRef.current.get(selectedFilePath),\n      dispatch: (transaction) => {\n        editorView.update([transaction]);\n        editorStatesRef.current.set(selectedFilePath, transaction.state);\n\n        if (transaction.docChanged) {\n          skipResetRef.current = true;\n          const newFiles = files.map((file) => {\n            if (file.path === selectedFilePath) {\n              return {\n                ...file,\n                content: transaction.state.doc.toString()\n              };\n            }\n            return file;\n          });\n          setFiles(newFiles);\n          \n          // Use setTimeout to allow state update to complete\n          setTimeout(() => {\n            skipResetRef.current = false;\n            updateFragmentURL();\n          }, 0);\n        }\n      }\n    });\n\n    editorViewRef.current = editorView;\n\n    return () => {\n      editorView.destroy();\n    };\n  }, []);\n\n  // Reset states when files change\n  useEffect(() => {\n    resetEditorStates();\n  }, [resetEditorStates]);\n\n  // Switch to selected file\n  useEffect(() => {\n    const state = editorStatesRef.current.get(selectedFilePath);\n    if (state && editorViewRef.current) {\n      editorViewRef.current.setState(state);\n    }\n  }, [selectedFilePath]);\n\n  // Update linter diagnostics\n  useEffect(() => {\n    const diagnostics = parseCompileLog(compileLog, files);\n    for (let fileIndex = 0; fileIndex < diagnostics.length; fileIndex++) {\n      const diagnosticsForFile = diagnostics[fileIndex];\n      const path = files[fileIndex].path;\n      const tr = {\n        effects: compartment.reconfigure(diagnostic.of(diagnosticsForFile))\n      };\n      \n      if (selectedFilePath === path) {\n        editorViewRef.current?.dispatch(tr);\n      } else {\n        // Update state in place for non-visible files\n        const state = editorStatesRef.current.get(path);\n        if (state) {\n          editorStatesRef.current.set(path, state.update(tr).state);\n        }\n      }\n    }\n  }, [compileLog, files, selectedFilePath]);\n\n  // Update theme\n  useEffect(() => {\n    const tr = {\n      effects: themeCompartmentRef.current.reconfigure(editorTheme)\n    };\n\n    // Update current file\n    editorViewRef.current?.dispatch(tr);\n    \n    // Update all other files\n    for (const [key, value] of editorStatesRef.current.entries()) {\n      editorStatesRef.current.set(key, value.update(tr).state);\n    }\n  }, [editorTheme]);\n\n  return <div ref={containerRef} className=\"grow overflow-hidden\" />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;AAdA;;;;;;;;;;;;;;;AAiBO,SAAS;;IACd,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC3B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA4B,IAAI;IAC7D,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,yJAAA,CAAA,cAAW;IAClD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EACJ,KAAK,EACL,gBAAgB,EAChB,WAAW,EACX,aAAa,EACb,UAAU,EACV,QAAQ,EACR,cAAc,EACd,gBAAgB,EACjB,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEf,MAAM,cAAc,UAAU,SAAS,gKAAA,CAAA,WAAQ,GAAG,4JAAA,CAAA,WAAQ;IAE1D,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,IAAM;gBACnC,8JAAA,CAAA,aAAU;gBACV,wJAAA,CAAA,SAAM,CAAC,EAAE,CAAC;oBAAC,4JAAA,CAAA,gBAAa;iBAAC;gBACzB,4JAAA,CAAA,aAAU,CAAC,EAAE,CAAC;gBACd,CAAA,GAAA,wJAAA,CAAA,aAAU,AAAD;gBACT,+HAAA,CAAA,cAAW,CAAC,EAAE,CAAC,+HAAA,CAAA,aAAU,CAAC,EAAE,CAAC,EAAE;gBAC/B,oBAAoB,OAAO,CAAC,EAAE,CAAC;aAChC;yCAAE;QAAC;KAAY;IAEhB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACpC,MAAM,aAAa,IAAI;YACvB,iBAAiB;YACjB,MAAM,oBAAoB,CAAA,GAAA,mIAAA,CAAA,WAAQ,AAAD,EAAE;gBACjC,OAAO;gBACP,SAAS;gBACT,OAAO;YACT;YACA,yDAAyD;YACzD,OAAO,OAAO,CAAC,YAAY,CAAC,MAAM,IAAI,AAAC,IAAqB,OAAlB;QAC5C;gDAAG;QAAC;QAAa;QAAO;KAAiB;IAEzC,wCAAwC;IACxC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YACpC,IAAI,aAAa,OAAO,EAAE;YAE1B,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,QAAQ,gBAAgB,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI;gBACjD,IAAI,OAAO;oBACT,mCAAmC;oBACnC,MAAM,WAAW,MAAM,GAAG,CAAC,QAAQ;oBACnC,IAAI,KAAK,OAAO,KAAK,UAAU;wBAC7B,MAAM,cAAc,MAAM,MAAM,CAAC;4BAC/B,SAAS;gCACP,MAAM;gCACN,IAAI,SAAS,MAAM;gCACnB,QAAQ,KAAK,OAAO;4BACtB;wBACF;wBACA,QAAQ,YAAY,KAAK;oBAC3B;gBACF,OAAO;oBACL,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;oBAC1C,QAAQ,yJAAA,CAAA,cAAW,CAAC,MAAM,CAAC;wBACzB,KAAK,KAAK,OAAO;wBACjB,YAAY,cAAc,SAAS;+BAAI;4BAAc,CAAA,GAAA,gKAAA,CAAA,OAAI,AAAD;yBAAI,GAAG;oBACjE;gBACF;gBAEA,gBAAgB,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;gBACvC,IAAI,KAAK,IAAI,KAAK,kBAAkB;wBAClC;qBAAA,yBAAA,cAAc,OAAO,cAArB,6CAAA,uBAAuB,QAAQ,CAAC;gBAClC;YACF;QACF;gDAAG;QAAC;QAAO;QAAkB;KAAW;IAExC,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,MAAM,aAAa,IAAI,wJAAA,CAAA,aAAU,CAAC;gBAChC,QAAQ,aAAa,OAAO;gBAC5B,OAAO,gBAAgB,OAAO,CAAC,GAAG,CAAC;gBACnC,QAAQ;wCAAE,CAAC;wBACT,WAAW,MAAM,CAAC;4BAAC;yBAAY;wBAC/B,gBAAgB,OAAO,CAAC,GAAG,CAAC,kBAAkB,YAAY,KAAK;wBAE/D,IAAI,YAAY,UAAU,EAAE;4BAC1B,aAAa,OAAO,GAAG;4BACvB,MAAM,WAAW,MAAM,GAAG;6DAAC,CAAC;oCAC1B,IAAI,KAAK,IAAI,KAAK,kBAAkB;wCAClC,OAAO;4CACL,GAAG,IAAI;4CACP,SAAS,YAAY,KAAK,CAAC,GAAG,CAAC,QAAQ;wCACzC;oCACF;oCACA,OAAO;gCACT;;4BACA,SAAS;4BAET,mDAAmD;4BACnD;oDAAW;oCACT,aAAa,OAAO,GAAG;oCACvB;gCACF;mDAAG;wBACL;oBACF;;YACF;YAEA,cAAc,OAAO,GAAG;YAExB;oCAAO;oBACL,WAAW,OAAO;gBACpB;;QACF;2BAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;KAAkB;IAEtB,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,QAAQ,gBAAgB,OAAO,CAAC,GAAG,CAAC;YAC1C,IAAI,SAAS,cAAc,OAAO,EAAE;gBAClC,cAAc,OAAO,CAAC,QAAQ,CAAC;YACjC;QACF;2BAAG;QAAC;KAAiB;IAErB,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;YAChD,IAAK,IAAI,YAAY,GAAG,YAAY,YAAY,MAAM,EAAE,YAAa;gBACnE,MAAM,qBAAqB,WAAW,CAAC,UAAU;gBACjD,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI;gBAClC,MAAM,KAAK;oBACT,SAAS,+HAAA,CAAA,cAAW,CAAC,WAAW,CAAC,+HAAA,CAAA,aAAU,CAAC,EAAE,CAAC;gBACjD;gBAEA,IAAI,qBAAqB,MAAM;wBAC7B;qBAAA,yBAAA,cAAc,OAAO,cAArB,6CAAA,uBAAuB,QAAQ,CAAC;gBAClC,OAAO;oBACL,8CAA8C;oBAC9C,MAAM,QAAQ,gBAAgB,OAAO,CAAC,GAAG,CAAC;oBAC1C,IAAI,OAAO;wBACT,gBAAgB,OAAO,CAAC,GAAG,CAAC,MAAM,MAAM,MAAM,CAAC,IAAI,KAAK;oBAC1D;gBACF;YACF;QACF;2BAAG;QAAC;QAAY;QAAO;KAAiB;IAExC,eAAe;IACf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;gBAKR,sBAAsB;YACtB;YALA,MAAM,KAAK;gBACT,SAAS,oBAAoB,OAAO,CAAC,WAAW,CAAC;YACnD;aAGA,yBAAA,cAAc,OAAO,cAArB,6CAAA,uBAAuB,QAAQ,CAAC;YAEhC,yBAAyB;YACzB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,gBAAgB,OAAO,CAAC,OAAO,GAAI;gBAC5D,gBAAgB,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,MAAM,CAAC,IAAI,KAAK;YACzD;QACF;2BAAG;QAAC;KAAY;IAEhB,qBAAO,6LAAC;QAAI,KAAK;QAAc,WAAU;;;;;;AAC3C;GAvKgB;;QAOI,mJAAA,CAAA,WAAQ;QAUtB,wIAAA,CAAA,eAAY;;;KAjBF", "debugId": null}}, {"offset": {"line": 1984, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/file-tab.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useReplStore, type File } from '@/lib/stores/repl-store';\n\ninterface FileTabProps {\n  file: File;\n  canEdit: boolean;\n}\n\nexport function FileTab({ file, canEdit }: FileTabProps) {\n  const [isEditing, setIsEditing] = useState(false);\n  const [path, setPath] = useState('');\n  const editableRef = useRef<HTMLButtonElement>(null);\n  \n  const { files, selectedFilePath, setFiles, setSelectedFilePath } = useReplStore();\n  \n  const isSelected = selectedFilePath === file.path;\n\n  // Update path when not editing\n  useEffect(() => {\n    if (!isEditing) {\n      setPath(file.path.substring(0, file.path.lastIndexOf('.')));\n    }\n  }, [file.path, isEditing]);\n\n  const handleClick = () => {\n    setSelectedFilePath(file.path);\n  };\n\n  const handleEditClick = () => {\n    if (isSelected) {\n      setIsEditing(true);\n    }\n  };\n\n  const handleFocusIn = () => {\n    setIsEditing(true);\n  };\n\n  const handleFocusOut = () => {\n    setIsEditing(false);\n    const newPath = path + '.java';\n    \n    if (newPath !== file.path) {\n      const existingFile = files.find((f) => f.path === newPath);\n      if (existingFile) {\n        alert(`A file with the name '${path}' already exists!`);\n        return;\n      }\n\n      const newFiles = files.map((f) => {\n        if (f.path === file.path) {\n          return { ...f, path: newPath };\n        }\n        return f;\n      });\n      \n      setFiles(newFiles);\n      setSelectedFilePath(newPath);\n    }\n  };\n\n  const handleDeleteClick = () => {\n    if (!confirm(`Are you sure you want to delete '${path}'?`)) {\n      return;\n    }\n    \n    const newFiles = files.filter((f) => f.path !== file.path);\n    setFiles(newFiles);\n    setSelectedFilePath(newFiles[0].path);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      editableRef.current?.blur();\n    }\n  };\n\n  return (\n    <button\n      className={`group px-3 py-2 hover:text-stone-800 dark:hover:text-stone-400 cursor-pointer flex items-center justify-center ${\n        isSelected \n          ? 'text-stone-600 dark:text-stone-200' \n          : ''\n      }`}\n      onClick={handleClick}\n    >\n      <button\n        ref={editableRef}\n        onClick={handleEditClick}\n        onFocus={handleFocusIn}\n        onBlur={handleFocusOut}\n        onKeyDown={handleKeyDown}\n        className={`focus:text-orange-600 ring-0 ${\n          !isSelected || !canEdit ? 'pointer-events-none' : ''\n        } ${isSelected ? 'cursor-text' : ''}`}\n        contentEditable={isEditing}\n        suppressContentEditableWarning={true}\n        onInput={(e) => setPath(e.currentTarget.textContent || '')}\n      >\n        {path}\n      </button>\n      {canEdit && (\n        <button\n          onClick={handleDeleteClick}\n          className={`ml-1 -mr-3 opacity-0 group-hover:opacity-50 ${\n            !isSelected ? 'opacity-0 pointer-events-none' : ''\n          }`}\n        >\n          <Icon icon=\"mi:close\" className=\"w-3 h-3\" />\n        </button>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWO,SAAS,QAAQ,KAA+B;QAA/B,EAAE,IAAI,EAAE,OAAO,EAAgB,GAA/B;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE9C,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAE9E,MAAM,aAAa,qBAAqB,KAAK,IAAI;IAEjD,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,CAAC,WAAW;gBACd,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,IAAI,CAAC,WAAW,CAAC;YACvD;QACF;4BAAG;QAAC,KAAK,IAAI;QAAE;KAAU;IAEzB,MAAM,cAAc;QAClB,oBAAoB,KAAK,IAAI;IAC/B;IAEA,MAAM,kBAAkB;QACtB,IAAI,YAAY;YACd,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,aAAa;QACb,MAAM,UAAU,OAAO;QAEvB,IAAI,YAAY,KAAK,IAAI,EAAE;YACzB,MAAM,eAAe,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK;YAClD,IAAI,cAAc;gBAChB,MAAM,AAAC,yBAA6B,OAAL,MAAK;gBACpC;YACF;YAEA,MAAM,WAAW,MAAM,GAAG,CAAC,CAAC;gBAC1B,IAAI,EAAE,IAAI,KAAK,KAAK,IAAI,EAAE;oBACxB,OAAO;wBAAE,GAAG,CAAC;wBAAE,MAAM;oBAAQ;gBAC/B;gBACA,OAAO;YACT;YAEA,SAAS;YACT,oBAAoB;QACtB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ,AAAC,oCAAwC,OAAL,MAAK,QAAM;YAC1D;QACF;QAEA,MAAM,WAAW,MAAM,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,KAAK,IAAI;QACzD,SAAS;QACT,oBAAoB,QAAQ,CAAC,EAAE,CAAC,IAAI;IACtC;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;gBACrB;aAAA,uBAAA,YAAY,OAAO,cAAnB,2CAAA,qBAAqB,IAAI;QAC3B;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,kHAIX,OAHC,aACI,uCACA;QAEN,SAAS;;0BAET,6LAAC;gBACC,KAAK;gBACL,SAAS;gBACT,SAAS;gBACT,QAAQ;gBACR,WAAW;gBACX,WAAW,AAAC,gCAER,OADF,CAAC,cAAc,CAAC,UAAU,wBAAwB,IACnD,KAAmC,OAAhC,aAAa,gBAAgB;gBACjC,iBAAiB;gBACjB,gCAAgC;gBAChC,SAAS,CAAC,IAAM,QAAQ,EAAE,aAAa,CAAC,WAAW,IAAI;0BAEtD;;;;;;YAEF,yBACC,6LAAC;gBACC,SAAS;gBACT,WAAW,AAAC,+CAEX,OADC,CAAC,aAAa,kCAAkC;0BAGlD,cAAA,6LAAC,wJAAA,CAAA,OAAI;oBAAC,MAAK;oBAAW,WAAU;;;;;;;;;;;;;;;;;AAK1C;GAzGgB;;QAKqD,wIAAA,CAAA,eAAY;;;KALjE", "debugId": null}}, {"offset": {"line": 2123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/file-tabs.tsx"], "sourcesContent": ["'use client';\n\nimport { FileTab } from './file-tab';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { Icon } from '@iconify/react';\n\nexport function FileTabs() {\n  const { files, setFiles, setSelectedFilePath } = useReplStore();\n\n  const getNewFilePath = (): string => {\n    let i = 2;\n    let path = `Class.java`;\n    while (files.find((f) => f.path === path)) {\n      path = `Class${i}.java`;\n      i++;\n    }\n    return path;\n  };\n\n  const handleAddFile = () => {\n    const path = getNewFilePath();\n    const className = path.replace(/\\.java$/, '');\n    const newFiles = [\n      ...files,\n      {\n        path,\n        content: `package fiddle;\n\nclass ${className} {\n    public ${className}() {\n        // TODO\n    }\n}\n`\n      }\n    ];\n    setFiles(newFiles);\n    setSelectedFilePath(path);\n  };\n\n  return (\n    <div className=\"flex items-stretch text-stone-500 h-8\">\n      {files.map((file, i) => (\n        <FileTab key={file.path} file={file} canEdit={i > 0} />\n      ))}\n      <button\n        className=\"mx-1\"\n        title=\"New file\"\n        onClick={handleAddFile}\n      >\n        <Icon icon=\"mi:add\" className=\"w-4 h-4 ml-2\" />\n      </button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAE5D,MAAM,iBAAiB;QACrB,IAAI,IAAI;QACR,IAAI,OAAQ;QACZ,MAAO,MAAM,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,MAAO;YACzC,OAAO,AAAC,QAAS,OAAF,GAAE;YACjB;QACF;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB;QACpB,MAAM,OAAO;QACb,MAAM,YAAY,KAAK,OAAO,CAAC,WAAW;QAC1C,MAAM,WAAW;eACZ;YACH;gBACE;gBACA,SAAS,AAAC,4BAGL,OADL,WAAU,mBACK,OAAV,WAAU;YAKjB;SACD;QACD,SAAS;QACT,oBAAoB;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,MAAM,GAAG,CAAC,CAAC,MAAM,kBAChB,6LAAC,4IAAA,CAAA,UAAO;oBAAiB,MAAM;oBAAM,SAAS,IAAI;mBAApC,KAAK,IAAI;;;;;0BAEzB,6LAAC;gBACC,WAAU;gBACV,OAAM;gBACN,SAAS;0BAET,cAAA,6LAAC,wJAAA,CAAA,OAAI;oBAAC,MAAK;oBAAS,WAAU;;;;;;;;;;;;;;;;;AAItC;GAhDgB;;QACmC,wIAAA,CAAA,eAAY;;;KAD/C", "debugId": null}}, {"offset": {"line": 2213, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/lib/utilities.ts"], "sourcesContent": ["// for adblockers protection\nexport function tryPlausible(msg: string) {\n  if (typeof window !== 'undefined' && (window as any).plausible) {\n    (window as any).plausible(msg);\n  }\n}\n\nexport function debounceFunction(fn: () => void, delay: number) {\n  let timeoutId: NodeJS.Timeout;\n  return () => {\n    clearTimeout(timeoutId);\n    timeoutId = setTimeout(() => fn(), delay);\n  };\n}\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;AACrB,SAAS,aAAa,GAAW;IACtC,IAAI,aAAkB,eAAe,AAAC,OAAe,SAAS,EAAE;QAC7D,OAAe,SAAS,CAAC;IAC5B;AACF;AAEO,SAAS,iBAAiB,EAAc,EAAE,KAAa;IAC5D,IAAI;IACJ,OAAO;QACL,aAAa;QACb,YAAY,WAAW,IAAM,MAAM;IACrC;AACF", "debugId": null}}, {"offset": {"line": 2239, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/cheerpj.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { debounceFunction } from '@/lib/utilities';\n\nexport function CheerpJ() {\n  const cjConsoleRef = useRef<HTMLElement | null>(null);\n  const cjOutputRef = useRef<HTMLElement | null>(null);\n  const cjOutputObserverRef = useRef<MutationObserver | null>(null);\n  const debounceRunCheerpjRef = useRef<(() => void) | null>(null);\n  \n  const {\n    files,\n    autoRun,\n    isRunning,\n    isSaved,\n    runCode,\n    setIsRunning,\n    setIsSaved,\n    setRunCode,\n    setCompileLog,\n    setFiles,\n  } = useReplStore();\n\n  const startCheerpj = async () => {\n    await cheerpjInit({\n      status: 'none',\n    });\n    const display = document.getElementById(\"output\");\n    cheerpjCreateDisplay(-1, -1, display);\n  };\n\n  const deriveMainClass = (file: { path: string; content: string }) => {\n    const match = file.content.match(/class\\s+(\\w+)/);\n    return match ? `fiddle.${match[1]}` : 'fiddle.Main';\n  };\n\n  const runCheerpj = async () => {\n    if (isRunning) return;\n\n    console.info('compileAndRun');\n    setIsRunning(true);\n    \n    if (cjConsoleRef.current) cjConsoleRef.current.innerHTML = '';\n    if (cjOutputRef.current) cjOutputRef.current.innerHTML = '';\n\n    const classPath = '/app/tools.jar:/files/';\n    const sourceFiles = files.map((file) => '/str/' + file.path);\n    \n    try {\n      const code = await cheerpjRunMain(\n        'com.sun.tools.javac.Main',\n        classPath,\n        ...sourceFiles,\n        '-d',\n        '/files/',\n        '-Xlint'\n      );\n      \n      if (code === 0) {\n        await cheerpjRunMain(deriveMainClass(files[0]), classPath);\n      }\n    } catch (error) {\n      console.error('CheerpJ execution error:', error);\n    }\n\n    // In case nothing is written on cjConsole and cjOutput\n    // manually unflag isRunning\n    if (isRunning) {\n      setIsRunning(false);\n    }\n    \n    if (cjConsoleRef.current) {\n      setCompileLog(cjConsoleRef.current.innerText);\n    }\n  };\n\n  useEffect(() => {\n    const initializeCheerpJ = async () => {\n      await startCheerpj();\n\n      cjConsoleRef.current = document.getElementById(\"console\");\n      cjOutputRef.current = document.getElementById(\"cheerpjDisplay\");\n      \n      // Remove useless loading screen\n      if (cjOutputRef.current) {\n        cjOutputRef.current.classList.remove(\"cheerpjLoading\");\n      }\n\n      // Create debounced version of runCheerpj\n      debounceRunCheerpjRef.current = debounceFunction(runCheerpj, 1000);\n\n      // Code execution (flagged by isRunning) is considered over\n      // when cjConsole or cjOutput are updated\n      cjOutputObserverRef.current = new MutationObserver(() => {\n        if (isRunning && (\n          (cjConsoleRef.current && cjConsoleRef.current.innerHTML) || \n          (cjOutputRef.current && cjOutputRef.current.innerHTML)\n        )) {\n          setIsRunning(false);\n          if (!isSaved) {\n            // Trigger files update to refresh state\n            setFiles([...files]);\n          }\n        }\n      });\n\n      if (cjConsoleRef.current) {\n        cjOutputObserverRef.current.observe(cjConsoleRef.current, {\n          childList: true,\n          subtree: true,\n        });\n      }\n\n      if (cjOutputRef.current) {\n        cjOutputObserverRef.current.observe(cjOutputRef.current, {\n          childList: true,\n          subtree: true,\n        });\n      }\n\n      // Initial run\n      await runCheerpj();\n    };\n\n    initializeCheerpJ();\n\n    return () => {\n      if (cjOutputObserverRef.current) {\n        cjOutputObserverRef.current.disconnect();\n      }\n    };\n  }, []);\n\n  // Handle file changes\n  useEffect(() => {\n    if (isRunning) {\n      setIsSaved(false);\n    } else {\n      try {\n        const encoder = new TextEncoder();\n        for (const file of files) {\n          cheerpOSAddStringFile('/str/' + file.path, encoder.encode(file.content));\n        }\n        setIsSaved(true);\n        if (autoRun) {\n          setRunCode(true);\n        }\n      } catch (error) {\n        console.error('Error writing files to CheerpJ', error);\n      }\n    }\n  }, [files, isRunning, autoRun, setIsSaved, setRunCode]);\n\n  // Handle run code trigger\n  useEffect(() => {\n    if (runCode) {\n      setRunCode(false);\n      if (autoRun && debounceRunCheerpjRef.current) {\n        debounceRunCheerpjRef.current();\n      } else {\n        runCheerpj();\n      }\n    }\n  }, [runCode, autoRun, setRunCode]);\n\n  return null; // This component doesn't render anything visible\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAMO,SAAS;;IACd,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAChD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAC/C,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA2B;IAC5D,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAE1D,MAAM,EACJ,KAAK,EACL,OAAO,EACP,SAAS,EACT,OAAO,EACP,OAAO,EACP,YAAY,EACZ,UAAU,EACV,UAAU,EACV,aAAa,EACb,QAAQ,EACT,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEf,MAAM,eAAe;QACnB,MAAM,YAAY;YAChB,QAAQ;QACV;QACA,MAAM,UAAU,SAAS,cAAc,CAAC;QACxC,qBAAqB,CAAC,GAAG,CAAC,GAAG;IAC/B;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAQ,KAAK,OAAO,CAAC,KAAK,CAAC;QACjC,OAAO,QAAQ,AAAC,UAAkB,OAAT,KAAK,CAAC,EAAE,IAAK;IACxC;IAEA,MAAM,aAAa;QACjB,IAAI,WAAW;QAEf,QAAQ,IAAI,CAAC;QACb,aAAa;QAEb,IAAI,aAAa,OAAO,EAAE,aAAa,OAAO,CAAC,SAAS,GAAG;QAC3D,IAAI,YAAY,OAAO,EAAE,YAAY,OAAO,CAAC,SAAS,GAAG;QAEzD,MAAM,YAAY;QAClB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAC,OAAS,UAAU,KAAK,IAAI;QAE3D,IAAI;YACF,MAAM,OAAO,MAAM,eACjB,4BACA,cACG,aACH,MACA,WACA;YAGF,IAAI,SAAS,GAAG;gBACd,MAAM,eAAe,gBAAgB,KAAK,CAAC,EAAE,GAAG;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;QAEA,uDAAuD;QACvD,4BAA4B;QAC5B,IAAI,WAAW;YACb,aAAa;QACf;QAEA,IAAI,aAAa,OAAO,EAAE;YACxB,cAAc,aAAa,OAAO,CAAC,SAAS;QAC9C;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;uDAAoB;oBACxB,MAAM;oBAEN,aAAa,OAAO,GAAG,SAAS,cAAc,CAAC;oBAC/C,YAAY,OAAO,GAAG,SAAS,cAAc,CAAC;oBAE9C,gCAAgC;oBAChC,IAAI,YAAY,OAAO,EAAE;wBACvB,YAAY,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;oBACvC;oBAEA,yCAAyC;oBACzC,sBAAsB,OAAO,GAAG,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;oBAE7D,2DAA2D;oBAC3D,yCAAyC;oBACzC,oBAAoB,OAAO,GAAG,IAAI;+DAAiB;4BACjD,IAAI,aAAa,CACf,AAAC,aAAa,OAAO,IAAI,aAAa,OAAO,CAAC,SAAS,IACtD,YAAY,OAAO,IAAI,YAAY,OAAO,CAAC,SAAS,AACvD,GAAG;gCACD,aAAa;gCACb,IAAI,CAAC,SAAS;oCACZ,wCAAwC;oCACxC,SAAS;2CAAI;qCAAM;gCACrB;4BACF;wBACF;;oBAEA,IAAI,aAAa,OAAO,EAAE;wBACxB,oBAAoB,OAAO,CAAC,OAAO,CAAC,aAAa,OAAO,EAAE;4BACxD,WAAW;4BACX,SAAS;wBACX;oBACF;oBAEA,IAAI,YAAY,OAAO,EAAE;wBACvB,oBAAoB,OAAO,CAAC,OAAO,CAAC,YAAY,OAAO,EAAE;4BACvD,WAAW;4BACX,SAAS;wBACX;oBACF;oBAEA,cAAc;oBACd,MAAM;gBACR;;YAEA;YAEA;qCAAO;oBACL,IAAI,oBAAoB,OAAO,EAAE;wBAC/B,oBAAoB,OAAO,CAAC,UAAU;oBACxC;gBACF;;QACF;4BAAG,EAAE;IAEL,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,WAAW;gBACb,WAAW;YACb,OAAO;gBACL,IAAI;oBACF,MAAM,UAAU,IAAI;oBACpB,KAAK,MAAM,QAAQ,MAAO;wBACxB,sBAAsB,UAAU,KAAK,IAAI,EAAE,QAAQ,MAAM,CAAC,KAAK,OAAO;oBACxE;oBACA,WAAW;oBACX,IAAI,SAAS;wBACX,WAAW;oBACb;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAClD;YACF;QACF;4BAAG;QAAC;QAAO;QAAW;QAAS;QAAY;KAAW;IAEtD,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,IAAI,SAAS;gBACX,WAAW;gBACX,IAAI,WAAW,sBAAsB,OAAO,EAAE;oBAC5C,sBAAsB,OAAO;gBAC/B,OAAO;oBACL;gBACF;YACF;QACF;4BAAG;QAAC;QAAS;QAAS;KAAW;IAEjC,OAAO,MAAM,iDAAiD;AAChE;GAlKgB;;QAiBV,wIAAA,CAAA,eAAY;;;KAjBF", "debugId": null}}, {"offset": {"line": 2410, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/output.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef } from 'react';\nimport { Icon } from '@iconify/react';\nimport { useReplStore } from '@/lib/stores/repl-store';\nimport { Loading } from '@/components/loading';\nimport { CheerpJ } from './cheerpj';\n\ninterface OutputProps {\n  isOutputMode?: boolean;\n}\n\nexport function Output({ isOutputMode = true }: OutputProps) {\n  const cjConsoleRef = useRef<HTMLPreElement>(null);\n  const { isRunning } = useReplStore();\n\n  const viewInOutputMode = () => {\n    if (typeof window !== 'undefined') {\n      const url = `/output/${window.location.hash}`;\n      window.open(url, \"_blank\", \"noreferrer\");\n    }\n  };\n\n  return (\n    <>\n      <div className={`w-full h-full ${!isRunning ? 'hidden' : ''}`}>\n        <Loading />\n      </div>\n\n      <div className={`w-full h-full flex ${isRunning ? 'hidden' : ''}`}>\n        <section className=\"flex flex-col w-1/2\">\n          <div className=\"p-3 text-stone-500 text-sm select-none\">Console</div>\n          <div className=\"grow relative overflow-auto\">\n            {/* CheerpJ implicitly looks for a #console to write to */}\n            <pre \n              ref={cjConsoleRef} \n              className=\"font-mono text-sm h-0\" \n              id=\"console\" \n            />\n          </div>\n        </section>\n        <section className=\"flex flex-col w-1/2\">\n          <div className=\"p-3 text-stone-500 text-sm select-none\">Result</div>\n          <div className=\"grow relative\" id=\"output\">\n            {/* #cheerpjDisplay will be inserted here */}\n          </div>\n        </section>\n      </div>\n\n      {isOutputMode && (\n        <div className=\"absolute top-1/2 right-0 text-stone-500 text-sm flex items-center select-none\">\n          <button \n            className=\"px-2 py-2\" \n            title=\"Open in new tab\" \n            onClick={viewInOutputMode}\n          >\n            <Icon icon=\"mi:external-link\" className=\"w-5 h-5\" />\n          </button>\n        </div>\n      )}\n\n      <style jsx global>{`\n        #cheerpjDisplay {\n          box-shadow: none;\n        }\n      `}</style>\n\n      <CheerpJ />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;;AAYO,SAAS,OAAO,KAAoC;QAApC,EAAE,eAAe,IAAI,EAAe,GAApC;;IACrB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAEjC,MAAM,mBAAmB;QACvB,wCAAmC;YACjC,MAAM,MAAM,AAAC,WAA+B,OAArB,OAAO,QAAQ,CAAC,IAAI;YAC3C,OAAO,IAAI,CAAC,KAAK,UAAU;QAC7B;IACF;IAEA,qBACE;;0BACE,6LAAC;yDAAe,AAAC,iBAA2C,OAA3B,CAAC,YAAY,WAAW;0BACvD,cAAA,6LAAC,gIAAA,CAAA,UAAO;;;;;;;;;;0BAGV,6LAAC;yDAAe,AAAC,sBAA+C,OAA1B,YAAY,WAAW;;kCAC3D,6LAAC;iEAAkB;;0CACjB,6LAAC;yEAAc;0CAAyC;;;;;;0CACxD,6LAAC;yEAAc;0CAEb,cAAA,6LAAC;oCACC,KAAK;oCAEL,IAAG;6EADO;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;iEAAkB;;0CACjB,6LAAC;yEAAc;0CAAyC;;;;;;0CACxD,6LAAC;gCAA8B,IAAG;yEAAnB;;;;;;;;;;;;;;;;;;YAMlB,8BACC,6LAAC;yDAAc;0BACb,cAAA,6LAAC;oBAEC,OAAM;oBACN,SAAS;6DAFC;8BAIV,cAAA,6LAAC,wJAAA,CAAA,OAAI;wBAAC,MAAK;wBAAmB,WAAU;;;;;;;;;;;;;;;;;;;;0BAW9C,6LAAC,wIAAA,CAAA,UAAO;;;;;;;AAGd;GA1DgB;;QAEQ,wIAAA,CAAA,eAAY;;;KAFpB", "debugId": null}}, {"offset": {"line": 2574, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/Javaistic/j/src/components/repl/repl.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useEffect } from \"react\";\nimport { Menu } from \"./menu\";\nimport { Sidebar } from \"./sidebar\";\nimport { Editor } from \"./editor\";\nimport { FileTabs } from \"./file-tabs\";\nimport { Output } from \"./output\";\nimport { useReplStore } from \"@/lib/stores/repl-store\";\nimport { tryPlausible } from \"@/lib/utilities\";\nimport SplitPane from \"react-split-pane\";\n\ninterface ReplProps {\n  enableSidebar?: boolean;\n  enableMenu?: boolean;\n}\n\nexport function Repl({ enableSidebar = true, enableMenu = true }: ReplProps) {\n  const { isSaved, setRunCode } = useReplStore();\n\n  const handleShare = async () => {\n    tryPlausible(\"Share\");\n    await navigator.clipboard.writeText(window.location.toString());\n  };\n\n  const handleRun = async () => {\n    tryPlausible(\"Compile\");\n    setRunCode(true);\n  };\n\n  const handleBeforeUnload = (e: BeforeUnloadEvent) => {\n    if (!isSaved) {\n      e.preventDefault();\n      e.returnValue = \"\";\n    }\n  };\n\n  useEffect(() => {\n    if (!isSaved) {\n      window.addEventListener(\"beforeunload\", handleBeforeUnload);\n      return () => {\n        window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n      };\n    }\n  }, [isSaved]);\n\n  return (\n    <div className=\"w-full h-screen font-sans flex flex-col overflow-hidden\">\n      {enableMenu && <Menu onShare={handleShare} onRun={handleRun} />}\n      <div className=\"flex items-stretch flex-1 overflow-hidden\">\n        {enableSidebar && <Sidebar />}\n        <div className=\"flex-1 overflow-hidden\">\n          <SplitPane split=\"horizontal\" minSize={64} defaultSize=\"50%\">\n            <div className=\"h-full flex flex-col\">\n              <div className=\"text-sm\">\n                <FileTabs />\n              </div>\n              <Editor />\n            </div>\n            <div className=\"border-t border-stone-200 dark:border-stone-700 overflow-hidden\">\n              <Output />\n            </div>\n          </SplitPane>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAiBO,SAAS,KAAK,KAAsD;QAAtD,EAAE,gBAAgB,IAAI,EAAE,aAAa,IAAI,EAAa,GAAtD;;IACnB,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAE3C,MAAM,cAAc;QAClB,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;QACb,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,OAAO,QAAQ,CAAC,QAAQ;IAC9D;IAEA,MAAM,YAAY;QAChB,CAAA,GAAA,0HAAA,CAAA,eAAY,AAAD,EAAE;QACb,WAAW;IACb;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,SAAS;YACZ,EAAE,cAAc;YAChB,EAAE,WAAW,GAAG;QAClB;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,SAAS;gBACZ,OAAO,gBAAgB,CAAC,gBAAgB;gBACxC;sCAAO;wBACL,OAAO,mBAAmB,CAAC,gBAAgB;oBAC7C;;YACF;QACF;yBAAG;QAAC;KAAQ;IAEZ,qBACE,6LAAC;QAAI,WAAU;;YACZ,4BAAc,6LAAC,qIAAA,CAAA,OAAI;gBAAC,SAAS;gBAAa,OAAO;;;;;;0BAClD,6LAAC;gBAAI,WAAU;;oBACZ,+BAAiB,6LAAC,wIAAA,CAAA,UAAO;;;;;kCAC1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,iKAAA,CAAA,UAAS;4BAAC,OAAM;4BAAa,SAAS;4BAAI,aAAY;;8CACrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6IAAA,CAAA,WAAQ;;;;;;;;;;sDAEX,6LAAC,uIAAA,CAAA,SAAM;;;;;;;;;;;8CAET,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,uIAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB;GAlDgB;;QACkB,wIAAA,CAAA,eAAY;;;KAD9B", "debugId": null}}]}