'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Icon } from '@iconify/react';
import { useReplStore } from '@/lib/stores/repl-store';
import { compress } from '@/lib/compress-fiddle';

export function FiddleTitle() {
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const router = useRouter();
  
  const { fiddleTitle, fiddleUpdated, files, setFiddleTitle } = useReplStore();

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  const handleBlur = () => {
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      setIsEditing(false);
      // Update URL since title changed
      const fiddleFragmentURL = compress({
        title: fiddleTitle,
        updated: fiddleUpdated,
        files: files
      });
      router.replace(`/#${fiddleFragmentURL}`);
    }
  };

  const handleEditClick = () => {
    setIsEditing(true);
  };

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="text"
        value={fiddleTitle}
        onChange={(e) => setFiddleTitle(e.target.value)}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className="text-lg block focus:outline-0 bg-inherit"
        autoComplete="off"
      />
    );
  }

  return (
    <h1 className="text-lg flex items-center gap-1.5">
      {fiddleTitle || 'Untitled'}
      <button onClick={handleEditClick}>
        <Icon icon="mi:edit" />
        <span className="sr-only">Edit title</span>
      </button>
    </h1>
  );
}
