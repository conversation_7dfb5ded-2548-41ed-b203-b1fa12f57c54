'use client';

import { useState, useEffect, useRef } from 'react';
import { Icon } from '@iconify/react';
import { SettingsPanel } from '@/components/settings/settings-panel';

export function SettingsButton() {
  const [isOpen, setIsOpen] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (panelRef.current && !panelRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [isOpen]);

  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(true);
  };

  const handlePanelClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  return (
    <>
      <button
        onClick={handleClick}
        className="text-sm rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-3 h-8 flex items-center gap-1"
      >
        <Icon icon="mi:filter" className="w-5 h-5" />
        Settings
      </button>

      {isOpen && (
        <div
          ref={panelRef}
          className="absolute top-[52px] right-4 z-10"
          onClick={handlePanelClick}
        >
          <SettingsPanel />
        </div>
      )}
    </>
  );
}
