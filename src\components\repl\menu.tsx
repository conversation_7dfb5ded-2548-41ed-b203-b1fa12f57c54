'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Icon } from '@iconify/react';
import TimeAgo from 'react-timeago';
import { FiddleTitle } from './menu/fiddle-title';
import { SettingsButton } from './menu/settings-button';
import { FavouriteButton } from './menu/favourite-button';
import { useReplStore } from '@/lib/stores/repl-store';
import { defaultFiddle, defaultFiddleComp } from '@/lib/compress-fiddle';

interface MenuProps {
  onShare: () => void;
  onRun: () => void;
}

export function Menu({ onShare, onRun }: MenuProps) {
  const [showShareMessage, setShowShareMessage] = useState(false);
  const router = useRouter();
  
  const {
    files,
    fiddleTitle,
    fiddleUpdated,
    favouriteIndex,
    autoRun,
    setFiles,
    setFiddleTitle,
    setFiddleUpdated,
    setFavouriteIndex,
  } = useReplStore();

  // Handle share message timeout
  useEffect(() => {
    if (showShareMessage) {
      const timeoutId = setTimeout(() => {
        setShowShareMessage(false);
      }, 800);
      return () => clearTimeout(timeoutId);
    }
  }, [showShareMessage]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        onShare();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onShare]);

  const createNewFile = () => {
    setFiles(defaultFiddle.files);
    setFiddleTitle(defaultFiddle.title);
    setFiddleUpdated(defaultFiddle.updated);
    if (favouriteIndex !== -1) {
      setFavouriteIndex(-1);
    }
    router.push(`/#${defaultFiddleComp}`);
  };

  const handleShare = () => {
    onShare();
    setShowShareMessage(true);
  };

  return (
    <header className="px-4 h-16 flex items-center justify-between gap-4 relative shadow dark:shadow-none dark:border-b border-b-stone-700 dark:bg-stone-800">
      <button 
        className="text-xl text-orange-500 dark:text-orange-400 font-bold" 
        onClick={createNewFile}
      >
        <h1>JavaFiddle</h1>
      </button>

      <div className="grow flex flex-col justify-center self-stretch">
        <FiddleTitle />
        {fiddleUpdated && (
          <div className="h-4 leading-3 text-xs text-stone-500 dark:text-stone-400">
            <TimeAgo date={fiddleUpdated} />
          </div>
        )}
      </div>

      <ul className="flex items-center gap-2">
        {showShareMessage && (
          <li className="text-xs text-stone-600 dark:text-stone-400">
            URL copied to clipboard
          </li>
        )}
        <FavouriteButton />
        {!autoRun && (
          <li>
            <button
              onClick={onRun}
              className="text-sm flex items-center rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-2 py-1 h-8"
            >
              <Icon icon="mi:play" className="w-5 h-5 mr-1" />
              Run
            </button>
          </li>
        )}
        <li>
          <button
            onClick={handleShare}
            className="text-sm flex items-center rounded bg-stone-100 hover:bg-stone-200 dark:bg-stone-800 dark:hover:bg-stone-700 font-semibold px-2 py-1 h-8"
            title="⌘S / Ctrl+S"
          >
            <Icon icon="mi:share" className="w-5 h-5 mr-1" />
            Share
          </button>
        </li>
        <li>
          <SettingsButton />
        </li>
      </ul>
    </header>
  );
}
